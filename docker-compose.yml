version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: apps/web/Dockerfile
      # Use BuildKit for better caching and performance
      cache_from:
        - node:22-alpine
      args:
        BUILDKIT_INLINE_CACHE: 1
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - HOSTNAME=0.0.0.0
    # Resource limits to prevent hanging
    deploy:
      resources:
        limits:
          memory: 10G  # Increased for build process
          cpus: '4.0'  # Increased for faster builds
        reservations:
          memory: 1G
          cpus: '1.0'
    # Health check
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  # Development service with hot reload
  web-dev:
    build:
      context: .
      dockerfile: apps/web/Dockerfile
      target: installer
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
      - /app/apps/web/node_modules
    environment:
      - NODE_ENV=development
    command: pnpm dev
    profiles:
      - dev
