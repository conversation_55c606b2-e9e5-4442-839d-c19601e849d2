# Mudanças no Sistema de Autenticação dos Gateways

## Resumo das Alterações

O sistema de autenticação foi atualizado para suportar corretamente os diferentes formatos de credenciais dos gateways PLUGGOU_PIX e FLOW2PAY.

## PLUGGOU_PIX

### Antes
```bash
PLUGGOU_PIX_API_KEY=your_api_key
PLUGGOU_PIX_API_SECRET=your_api_secret  # ❌ Removido
PLUGGOU_PIX_BASE_URL=https://api.pluggou.io  # ❌ Removido
PLUGGOU_PIX_WEBHOOK_SECRET=your_webhook_secret
```

### Agora
```bash
PLUGGOU_PIX_API_KEY=your_api_key  # ✅ Único campo necessário
PLUGGOU_PIX_API_URL=https://apipix.cloud.pluggou.io  # ✅ URL correta
PLUGGOU_PIX_ENVIRONMENT=sandbox|production
PLUGGOU_PIX_WEBHOOK_SECRET=your_webhook_secret
```

**Motivo**: O PLUGGOU_PIX usa apenas `apiKey` para autenticação, não precisa de `apiSecret`.

## FLOW2PAY

### Antes
```bash
FLOW2PAY_API_KEY=your_api_key  # ❌ Removido
FLOW2PAY_API_SECRET=your_api_secret  # ❌ Removido
FLOW2PAY_BASE_URL=https://api.flow2pay.com  # ❌ Removido
FLOW2PAY_WEBHOOK_SECRET=your_webhook_secret  # ❌ Removido
```

### Agora
```bash
FLOW2PAY_CLIENT_ID=your_client_id  # ✅ Necessário para OAuth
FLOW2PAY_CLIENT_SECRET=your_client_secret  # ✅ Necessário para OAuth
FLOW2PAY_EVENT_TOKEN=your_event_token  # ✅ Necessário para webhooks
FLOW2PAY_API_URL=https://pixv2.flow2pay.com.br  # ✅ URL correta
FLOW2PAY_ENVIRONMENT=sandbox|production
FLOW2PAY_TIMEOUT=15000  # ✅ Timeout configurável
FLOW2PAY_RETRIES=2  # ✅ Retry configurável
```

**Motivo**: O FLOW2PAY usa OAuth com `clientId` e `clientSecret`, além de `eventToken` para webhooks.

## Arquivos Alterados

### 1. `packages/payments/provider/factory.ts`
- Atualizada função `getEnvironmentCredentials()` para usar as credenciais corretas
- PLUGGOU_PIX: apenas `apiKey` e `apiUrl`
- FLOW2PAY: `clientId`, `clientSecret`, `eventToken`, `apiUrl`, `timeout`, `retries`

### 2. `env.example`
- Atualizadas as variáveis de ambiente de exemplo
- Removidas credenciais desnecessárias
- Adicionadas novas credenciais necessárias

### 3. `GATEWAY_CONFIGURATION.md`
- Atualizada documentação com as novas credenciais
- Exemplos corrigidos para ambos os gateways

### 4. Scripts de configuração
- `scripts/fix-pluggou-gateway-configuration.ts`
- `scripts/fix-pix-transfer-system.ts`
- Removido `clientId` desnecessário do PLUGGOU_PIX
- Corrigida URL padrão

## Compatibilidade

### ✅ Compatível com versões anteriores
- O sistema continua funcionando com credenciais do banco de dados
- Fallback automático se variáveis de ambiente não estiverem definidas

### ⚡ Performance otimizada
- Quando as variáveis de ambiente estão definidas, elimina consultas ao banco
- Cache de credenciais por 5 minutos
- Logs detalhados para monitoramento

## Migração

### Para PLUGGOU_PIX
```bash
# Remover (se existir)
unset PLUGGOU_PIX_API_SECRET
unset PLUGGOU_PIX_BASE_URL

# Adicionar/atualizar
export PLUGGOU_PIX_API_KEY=your_actual_api_key
export PLUGGOU_PIX_API_URL=https://apipix.cloud.pluggou.io
```

### Para FLOW2PAY
```bash
# Remover (se existir)
unset FLOW2PAY_API_KEY
unset FLOW2PAY_API_SECRET
unset FLOW2PAY_BASE_URL
unset FLOW2PAY_WEBHOOK_SECRET

# Adicionar
export FLOW2PAY_CLIENT_ID=your_client_id
export FLOW2PAY_CLIENT_SECRET=your_client_secret
export FLOW2PAY_EVENT_TOKEN=your_event_token
export FLOW2PAY_API_URL=https://pixv2.flow2pay.com.br
export FLOW2PAY_TIMEOUT=15000
export FLOW2PAY_RETRIES=2
```

## Verificação

Para verificar se as credenciais estão corretas, observe os logs:

```bash
# PLUGGOU_PIX correto
🚀 Using environment credentials for default gateway PLUGGOU_PIX
✅ Environment credentials found for PLUGGOU_PIX
keys: ["apiKey", "apiUrl", "environment", "webhookSecret"]

# FLOW2PAY correto
🚀 Using environment credentials for default gateway FLOW2PAY
✅ Environment credentials found for FLOW2PAY
keys: ["clientId", "clientSecret", "eventToken", "apiUrl", "environment", "timeout", "retries"]
```

## Próximos Passos

1. **Testar PLUGGOU_PIX**: Verificar se funciona apenas com `apiKey`
2. **Migrar para FLOW2PAY**: Quando necessário, usar as novas credenciais OAuth
3. **Monitorar logs**: Verificar se as credenciais estão sendo carregadas corretamente
4. **Performance**: Aproveitar a otimização de velocidade com variáveis de ambiente
