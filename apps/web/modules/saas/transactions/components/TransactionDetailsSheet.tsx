"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@ui/components/sheet";
import { ArrowDownLeft, ArrowUpR<PERSON>, Copy, Loader2, X } from "lucide-react";
import { cn } from "@ui/lib";
import { Button } from "@ui/components/button";
import { useTheme } from "next-themes";
import { toast } from "sonner";
import { QRCodeSVG } from "qrcode.react";

import { Transaction, TransactionStatus } from "../hooks/use-transactions";

export type TransactionDetails = Transaction & {
  // Campos adicionais para o sheet de detalhes
  organizationId: string;
  description?: string;
  fee?: string | number | null;
  platformFee?: string | number | null;
  securityReserve?: string | number | null;
  commission?: string | number | null;
  netAmount?: string | number | null;
  // Dedicated fee fields
  percentFee?: number;
  fixedFee?: number;
  totalFee?: number;
  pixCode?: string;
  pixKey?: string;
  pixKeyType?: "CPF" | "EMAIL" | "PHONE" | "RANDOM" | "CNPJ";
  receiverName?: string;
  receiverDocument?: string;
  receiverBank?: string;
  // PIX specific identifiers
  endToEndId?: string | null;
  pixEndToEndId?: string | null;
  refunds?: Array<{
    id: string;
    amount: number;
    status: TransactionStatus;
    reason?: string;
    createdAt: string;
    processedAt?: string;
  }>;
  blocks?: Array<{
    id: string;
    reason: string;
    status: string;
    createdAt: string;
    releasedAt?: string;
  }>;
  customerPhone?: string;
  customerDocument?: string;
  customerDocumentType?: string;
  // Dados adicionais do PIX conforme documentação
  paymentDetails?: {
    pixEndToEndId?: string | null;
    endToEndId?: string | null;
    pixQrCode?: string | null;
    pixPayload?: string | null;
    pixEncodedImage?: string | null;
    pixExpirationDate?: string | null;
    pixReceiptUrl?: string | null;
    authorizationCode?: string | null;
    receiverName?: string | null;
    receiverDocument?: string | null;
    receiverBank?: string | null;
  };
  metadata?: Record<string, any> | null;
};

type TransactionDetailsSheetProps = {
  isOpen: boolean;
  onClose: () => void;
  transaction: TransactionDetails | null;
};

export function TransactionDetailsSheet({
  isOpen,
  onClose,
  transaction
}: TransactionDetailsSheetProps) {
  const t = useTranslations();
  const { theme } = useTheme();
  const [isCheckingPayment, setIsCheckingPayment] = useState(false);
  const [expandedValues, setExpandedValues] = useState<Record<string, boolean>>({});

  // Se não houver transação ou não estiver aberto, não renderize nada
  if (!transaction || !isOpen) return null;

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success("Copiado para a área de transferência");
  };

  const toggleExpand = (key: string) => {
    setExpandedValues(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Função para verificar o status do pagamento
  const checkPaymentStatus = async () => {
    if (!transaction.id) return;

    setIsCheckingPayment(true);

    try {
      // For Pluggou PIX transactions, we should use externalId if available
      const idToSync = transaction.externalId || transaction.metadata?.paymentDetails?.txid ||
                      transaction.metadata?.pixRawPayload?.txid || transaction.id;

      const response = await fetch(`/api/payments/transactions/sync-status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactionId: idToSync,
          originalTransactionId: transaction.id, // The internal ID to update
        }),
      });

      console.log("Response:", response);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: "Erro desconhecido" }));
        console.error("Error syncing payment:", errorData);
        throw new Error(errorData.message || 'Erro ao verificar status do pagamento');
      }

      const data = await response.json();

      if (data.success) {
        toast.success("Status do pagamento atualizado");
        // Recarregar a página para atualizar os dados
        window.location.reload();
      } else {
        toast.error(data.message || "Erro ao verificar status do pagamento");
      }
    } catch (error) {
      console.error("Erro ao verificar status:", error);

      // Show a more descriptive error message
      if (error instanceof Error && error.message.includes("not found")) {
        toast.error("Transação não encontrada no gateway de pagamento. Use o ID da transação do recebedor.");
      } else {
        toast.error(`Erro ao verificar status do pagamento: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
      }
    } finally {
      setIsCheckingPayment(false);
    }
  };

  const getStatusBadge = () => {
    switch (transaction.status) {
      case "APPROVED":
        return (
          <span className="bg-success/90 dark:bg-success/80 text-success-foreground dark:text-success-foreground text-xs font-medium px-4 py-1 rounded-full">
            Aprovado
          </span>
        );
      case "REJECTED":
      case "CANCELED":
      case "BLOCKED":
        return (
          <span className="bg-destructive/90 dark:bg-destructive/80 text-destructive-foreground dark:text-destructive-foreground text-xs font-medium px-4 py-1 rounded-full">
            Rejeitado
          </span>
        );
      case "REFUNDED":
        return (
          <span className="bg-default/90 dark:bg-default/80 text-default-foreground dark:text-default-foreground text-xs font-medium px-4 py-1 rounded-full">
            Estornado
          </span>
        );
      default:
        return (
          <span className="bg-highlight/90 dark:bg-highlight/80 text-highlight-foreground dark:text-highlight-foreground text-xs font-medium px-4 py-1 rounded-full">
            Pendente
          </span>
        );
    }
  };

  const getTransactionTypeIcon = () => {
    const isIncoming = transaction.type === "RECEIVE";
    return isIncoming ? (
      <div className="p-2 bg-success/10 dark:bg-success/20 rounded-full text-success dark:text-primary">
        <ArrowDownLeft size={18} />
      </div>
    ) : (
      <div className="p-2 bg-destructive/10 dark:bg-destructive/20 rounded-full text-destructive dark:text-destructive">
        <ArrowUpRight size={18} />
      </div>
    );
  };

  const formatPixKeyType = (type?: string) => {
    switch (type) {
      case "CPF": return "CPF";
      case "CNPJ": return "CNPJ";
      case "EMAIL": return "E-mail";
      case "PHONE": return "Telefone";
      case "RANDOM": return "Chave aleatória";
      default: return "Chave PIX";
    }
  };

  // Formatar data
  const formatDate = (dateString?: string | null) => {
    if (!dateString) return "-";
    try {
      const date = new Date(dateString);
      return new Intl.DateTimeFormat('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).format(date);
    } catch (e) {
      return dateString;
    }
  };

  // Formatar valor
  const formatCurrency = (value?: number) => {
    if (value === undefined) return "-";
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  return (
    <Sheet open={isOpen} onOpenChange={() => onClose()}>
      <SheetContent side="right" className="w-full sm:max-w-md md:max-w-lg lg:max-w-xl p-0 border-l border-border dark:border-border bg-background/80 dark:bg-background/80 backdrop-blur-sm">
        <div className="flex flex-col h-full">
          {/* Cabeçalho */}
          <div className="p-4 sm:p-5 border-b border-border dark:border-border flex items-center justify-between">
            <SheetTitle className="text-xl font-semibold text-foreground dark:text-foreground">
              Detalhes da Transação
            </SheetTitle>
          </div>

          {/* Conteúdo */}
          <div className="flex-1 overflow-auto">
            <div className="p-4 sm:p-5 space-y-5">
              {/* ID da Transação e Status */}
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  {getTransactionTypeIcon()}
                  <div>
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Transação</div>
                    <div className="font-medium text-foreground dark:text-foreground flex items-center gap-1 max-w-[180px] truncate"
                         title={transaction.referenceCode || transaction.id}>
                      {transaction.referenceCode || transaction.id}
                      <button
                        onClick={() => copyToClipboard(transaction.referenceCode || transaction.id)}
                        className="text-muted-foreground hover:text-foreground dark:text-muted-foreground dark:hover:text-foreground flex-shrink-0"
                      >
                        <Copy size={14} />
                      </button>
                    </div>
                  </div>
                </div>
                <div>
                  {getStatusBadge()}
                </div>
              </div>

              {/* Datas */}
              <div className="space-y-3 bg-accent/20 dark:bg-accent/10 rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <div className="text-sm font-medium text-muted-foreground dark:text-muted-foreground">Realizada</div>
                  <div className="text-sm text-foreground dark:text-foreground">{formatDate(transaction.createdAt)}</div>
                </div>
                <div className="flex justify-between items-center">
                  <div className="text-sm font-medium text-muted-foreground dark:text-muted-foreground">Pagamento</div>
                  <div className="text-sm text-foreground dark:text-foreground">{formatDate(transaction.paymentAt)}</div>
                </div>
              </div>

              {/* Dados da Transação */}
              <div className="bg-accent/20 dark:bg-accent/10 p-4 rounded-lg space-y-3">
                <h3 className="font-medium text-foreground dark:text-foreground">Dados da transação</h3>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground dark:text-muted-foreground">Subtotal</div>
                  <div className="text-sm font-medium text-foreground dark:text-foreground">{formatCurrency(transaction.amount)}</div>
                </div>
                {/* Display transaction fees */}
                {(transaction.totalFee && transaction.totalFee > 0) || transaction.fee ? (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Taxa por Transação</div>
                    <div className="text-sm font-medium text-destructive dark:text-destructive">
                      -{transaction.totalFee && transaction.totalFee > 0 ? formatCurrency(transaction.totalFee) :
                        transaction.fee ? (typeof transaction.fee === 'number' ? formatCurrency(transaction.fee) : transaction.fee) : '-'}
                    </div>
                  </div>
                ) : null}
                {transaction.fixedFee && transaction.fixedFee > 0 && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Taxa Fixa</div>
                    <div className="text-sm font-medium text-destructive dark:text-destructive">-{formatCurrency(transaction.fixedFee)}</div>
                  </div>
                )}
                {transaction.percentFee && transaction.percentFee > 0 && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Taxa Percentual</div>
                    <div className="text-sm font-medium text-destructive dark:text-destructive">-{formatCurrency(transaction.percentFee)}</div>
                  </div>
                )}
                {/* PIX End-to-End ID */}
                {transaction.endToEndId && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">PIX End-to-End ID</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground flex items-center gap-1">
                      <span className="max-w-[180px] truncate">
                        {transaction.endToEndId}
                      </span>
                      <button
                        onClick={() => copyToClipboard(transaction.endToEndId || "")}
                        className="text-muted-foreground hover:text-foreground"
                      >
                        <Copy size={14} />
                      </button>
                    </div>
                  </div>
                )}
                {transaction.platformFee && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Taxa da Plataforma</div>
                    <div className="text-sm font-medium text-destructive dark:text-destructive">{transaction.platformFee}</div>
                  </div>
                )}
                {transaction.securityReserve && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Reserva de Segurança</div>
                    <div className="text-sm font-medium text-destructive dark:text-destructive">{transaction.securityReserve}</div>
                  </div>
                )}
                {transaction.commission && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Comissão</div>
                    <div className="text-sm font-medium text-success dark:text-primary">{transaction.commission}</div>
                  </div>
                )}
                {transaction.netAmount && (
                  <>
                    <hr className="border-border dark:border-border/50" />
                    <div className="flex justify-between items-center">
                      <div className="text-sm font-semibold text-foreground dark:text-foreground">Valor Líquido</div>
                      <div className="text-sm font-semibold text-success dark:text-primary">{typeof transaction.netAmount === 'number' ? formatCurrency(transaction.netAmount) : transaction.netAmount}</div>
                    </div>
                  </>
                )}
              </div>

              {/* Dados do PIX */}
              <div className="bg-accent/20 dark:bg-accent/10 p-4 rounded-lg space-y-3">
                <h3 className="font-medium text-foreground dark:text-foreground">Dados do PIX</h3>

                {/* QR Code para transações pendentes */}
                {transaction.status === "PENDING" && (
                  <div className="flex flex-col items-center justify-center py-2">
                    <div className="bg-white p-4 rounded-lg mb-2 shadow-sm">
                      {transaction.paymentDetails?.pixEncodedImage ? (
                        <img
                          src={typeof transaction.paymentDetails.pixEncodedImage === 'string' && transaction.paymentDetails.pixEncodedImage.startsWith('data:')
                            ? transaction.paymentDetails.pixEncodedImage
                            : typeof transaction.paymentDetails.pixEncodedImage === 'string'
                              ? `data:image/png;base64,${transaction.paymentDetails.pixEncodedImage}`
                              : ''}
                          alt="QR Code PIX"
                          width={150}
                          height={150}
                          className="rounded-lg"
                        />
                      ) : transaction.metadata?.pixQrCode || transaction.metadata?.pixEncodedImage ? (
                        <img
                          src={typeof transaction.metadata.pixQrCode === 'string' && transaction.metadata.pixQrCode.startsWith('data:')
                            ? transaction.metadata.pixQrCode
                            : typeof transaction.metadata.pixEncodedImage === 'string' && transaction.metadata.pixEncodedImage.startsWith('data:')
                              ? transaction.metadata.pixEncodedImage
                              : typeof transaction.metadata.pixQrCode === 'string'
                                ? `data:image/png;base64,${transaction.metadata.pixQrCode}`
                                : typeof transaction.metadata.pixEncodedImage === 'string'
                                  ? `data:image/png;base64,${transaction.metadata.pixEncodedImage}`
                                  : ''}
                          alt="QR Code PIX"
                          width={150}
                          height={150}
                          className="rounded-lg"
                        />
                      ) : transaction.paymentDetails?.pixPayload ? (
                        <QRCodeSVG
                          value={typeof transaction.paymentDetails.pixPayload === 'string' ? transaction.paymentDetails.pixPayload : ''}
                          size={150}
                          bgColor={"#ffffff"}
                          fgColor={"#000000"}
                          level={"L"}
                          className="rounded-sm"
                        />
                      ) : transaction.metadata?.pixPayload || transaction.metadata?.pixCode || transaction.pixCode ? (
                        <QRCodeSVG
                          value={typeof transaction.metadata?.pixPayload === 'string'
                            ? transaction.metadata.pixPayload
                            : typeof transaction.metadata?.pixCode === 'string'
                              ? transaction.metadata.pixCode
                              : typeof transaction.pixCode === 'string'
                                ? transaction.pixCode
                                : ""}
                          size={150}
                          bgColor={"#ffffff"}
                          fgColor={"#000000"}
                          level={"L"}
                          className="rounded-sm"
                        />
                      ) : null}
                    </div>
                    <p className="text-xs text-muted-foreground mb-2">Escaneie o QR Code com o app do seu banco</p>
                  </div>
                )}

                {/* Código PIX para copiar e colar */}
                {transaction.status === "PENDING" && (
                  transaction.paymentDetails?.pixPayload ||
                  transaction.metadata?.pixPayload ||
                  transaction.metadata?.pixCode ||
                  transaction.pixCode
                ) && (
                  <div className="space-y-2 mb-3">
                    <div className="flex justify-between items-center">
                      <p className="text-xs font-medium">Código PIX para copiar e colar:</p>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 px-2"
                        onClick={() => copyToClipboard(
                          typeof transaction.paymentDetails?.pixPayload === 'string'
                            ? transaction.paymentDetails.pixPayload
                            : typeof transaction.metadata?.pixPayload === 'string'
                              ? transaction.metadata.pixPayload
                              : typeof transaction.metadata?.pixCode === 'string'
                                ? transaction.metadata.pixCode
                                : typeof transaction.pixCode === 'string'
                                  ? transaction.pixCode
                                  : ""
                        )}
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        Copiar
                      </Button>
                    </div>
                    <div
                      className={cn(
                        "bg-muted p-2 rounded-md text-xs border border-border cursor-pointer transition-all duration-200",
                        expandedValues["pixCode"] ? "max-h-48 overflow-y-auto break-all" : "max-h-24 overflow-hidden text-ellipsis break-all"
                      )}
                      onClick={() => toggleExpand("pixCode")}
                      title={expandedValues["pixCode"] ? "Clique para recolher" : "Clique para expandir"}
                    >
                      {typeof transaction.paymentDetails?.pixPayload === 'string'
                        ? transaction.paymentDetails.pixPayload
                        : typeof transaction.metadata?.pixPayload === 'string'
                          ? transaction.metadata.pixPayload
                          : typeof transaction.metadata?.pixCode === 'string'
                            ? transaction.metadata.pixCode
                            : typeof transaction.pixCode === 'string'
                              ? transaction.pixCode
                              : ""}
                    </div>
                    <div className="text-xs text-center text-muted-foreground">
                      {expandedValues["pixCode"] ? "Clique para recolher" : "Clique para expandir"}
                    </div>
                  </div>
                )}

                {transaction.pixKeyType && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Tipo de Chave</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground">{formatPixKeyType(transaction.pixKeyType)}</div>
                  </div>
                )}

                {transaction.pixKey && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Chave PIX</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground flex items-center gap-1 max-w-[60%] truncate">
                      {transaction.pixKey}
                      <button
                        onClick={() => copyToClipboard(transaction.pixKey || "")}
                        className="text-muted-foreground hover:text-foreground dark:text-muted-foreground dark:hover:text-foreground flex-shrink-0"
                      >
                        <Copy size={14} />
                      </button>
                    </div>
                  </div>
                )}

                {/* Identificador E2E (End-to-End ID) */}
                {(transaction.paymentDetails?.pixEndToEndId ||
                  (transaction.metadata?.paymentDetails && typeof transaction.metadata.paymentDetails === 'object' && 'endToEndId' in transaction.metadata.paymentDetails) ||
                  transaction.metadata?.pixEndToEndId) && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">ID End-to-End</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground flex items-center gap-1 max-w-[60%] truncate">
                      {transaction.paymentDetails?.pixEndToEndId ||
                       (transaction.metadata?.paymentDetails && typeof transaction.metadata.paymentDetails === 'object' && 'endToEndId' in transaction.metadata.paymentDetails ?
                        String(transaction.metadata.paymentDetails.endToEndId) :
                        transaction.metadata?.pixEndToEndId ? String(transaction.metadata.pixEndToEndId) : '')}
                      <button
                        onClick={() => copyToClipboard(
                          transaction.paymentDetails?.pixEndToEndId ||
                          (transaction.metadata?.paymentDetails && typeof transaction.metadata.paymentDetails === 'object' && 'endToEndId' in transaction.metadata.paymentDetails ?
                           String(transaction.metadata.paymentDetails.endToEndId) :
                           transaction.metadata?.pixEndToEndId ? String(transaction.metadata.pixEndToEndId) : '')
                        )}
                        className="text-muted-foreground hover:text-foreground dark:text-muted-foreground dark:hover:text-foreground flex-shrink-0"
                      >
                        <Copy size={14} />
                      </button>
                    </div>
                  </div>
                )}

                {/* Dados do pagador do PIX */}
                {(transaction.metadata?.paymentDetails?.payer?.name ||
                  (transaction.metadata?.pixPayer && typeof transaction.metadata.pixPayer === 'object' && 'nome' in transaction.metadata.pixPayer && transaction.metadata.pixPayer.nome)) && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Pagador</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground max-w-[60%] truncate">
                      {transaction.metadata?.paymentDetails?.payer?.name ||
                       (transaction.metadata?.pixPayer && typeof transaction.metadata.pixPayer === 'object' && 'nome' in transaction.metadata.pixPayer ?
                        String(transaction.metadata.pixPayer.nome) : '')}
                    </div>
                  </div>
                )}

                {(transaction.metadata?.paymentDetails?.payer?.document ||
                  (transaction.metadata?.pixPayer && typeof transaction.metadata.pixPayer === 'object' &&
                   ('cpf_cnpj' in transaction.metadata.pixPayer || 'cpf' in transaction.metadata.pixPayer))) && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Documento do Pagador</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground">
                      {transaction.metadata?.paymentDetails?.payer?.document ||
                       (transaction.metadata?.pixPayer && typeof transaction.metadata.pixPayer === 'object' && 'cpf_cnpj' in transaction.metadata.pixPayer ?
                        String(transaction.metadata.pixPayer.cpf_cnpj) :
                        transaction.metadata?.pixPayer && typeof transaction.metadata.pixPayer === 'object' && 'cpf' in transaction.metadata.pixPayer ?
                        String(transaction.metadata.pixPayer.cpf) : '')}
                    </div>
                  </div>
                )}

                {(transaction.metadata?.paymentDetails?.payer?.bankCode ||
                  (transaction.metadata?.pixPayer && typeof transaction.metadata.pixPayer === 'object' && 'codigoBanco' in transaction.metadata.pixPayer)) && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Banco do Pagador</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground">
                      {transaction.metadata?.paymentDetails?.payer?.bankCode ||
                       (transaction.metadata?.pixPayer && typeof transaction.metadata.pixPayer === 'object' && 'codigoBanco' in transaction.metadata.pixPayer ?
                        String(transaction.metadata.pixPayer.codigoBanco) : '')}
                    </div>
                  </div>
                )}

                {/* TxId do PIX */}
                {(transaction.metadata?.paymentDetails?.txid ||
                  (transaction.metadata?.pixRawPayload && typeof transaction.metadata.pixRawPayload === 'object' && 'txid' in transaction.metadata.pixRawPayload)) && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">TxID</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground flex items-center gap-1 max-w-[60%] truncate">
                      {transaction.metadata?.paymentDetails?.txid ||
                       (transaction.metadata?.pixRawPayload && typeof transaction.metadata.pixRawPayload === 'object' && 'txid' in transaction.metadata.pixRawPayload ?
                        String(transaction.metadata.pixRawPayload.txid) : '')}
                      <button
                        onClick={() => copyToClipboard(
                          transaction.metadata?.paymentDetails?.txid ||
                          (transaction.metadata?.pixRawPayload && typeof transaction.metadata.pixRawPayload === 'object' && 'txid' in transaction.metadata.pixRawPayload ?
                           String(transaction.metadata.pixRawPayload.txid) : '')
                        )}
                        className="text-muted-foreground hover:text-foreground dark:text-muted-foreground dark:hover:text-foreground flex-shrink-0"
                      >
                        <Copy size={14} />
                      </button>
                    </div>
                  </div>
                )}

                {/* Código de autorização */}
                {transaction.paymentDetails?.authorizationCode && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Código de Autorização</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground">{transaction.paymentDetails.authorizationCode}</div>
                  </div>
                )}

                {/* Data de expiração do PIX */}
                {transaction.paymentDetails?.pixExpirationDate && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Expira em</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground">{formatDate(transaction.paymentDetails.pixExpirationDate)}</div>
                  </div>
                )}

                {transaction.receiverName && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Destinatário</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground">{transaction.receiverName}</div>
                  </div>
                )}

                {transaction.paymentDetails?.receiverName && !transaction.receiverName && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Destinatário</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground">{transaction.paymentDetails.receiverName}</div>
                  </div>
                )}

                {transaction.receiverDocument && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Documento</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground">{transaction.receiverDocument}</div>
                  </div>
                )}

                {transaction.paymentDetails?.receiverDocument && !transaction.receiverDocument && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Documento</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground">{transaction.paymentDetails.receiverDocument}</div>
                  </div>
                )}

                {transaction.receiverBank && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Instituição</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground">{transaction.receiverBank}</div>
                  </div>
                )}

                {transaction.paymentDetails?.receiverBank && !transaction.receiverBank && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Instituição</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground">{transaction.paymentDetails.receiverBank}</div>
                  </div>
                )}

                {/* Link do comprovante, se disponível */}
                {transaction.paymentDetails?.pixReceiptUrl && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Comprovante</div>
                    <a
                      href={transaction.paymentDetails.pixReceiptUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm font-medium text-primary hover:underline"
                    >
                      Visualizar comprovante
                    </a>
                  </div>
                )}
              </div>


              {/* Dados do Cliente */}
              <div className="bg-accent/20 dark:bg-accent/10 p-4 rounded-lg space-y-3">
                <h3 className="font-medium text-foreground dark:text-foreground">Dados do cliente</h3>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground dark:text-muted-foreground">Nome</div>
                  <div className="text-sm font-medium text-foreground dark:text-foreground max-w-[60%] truncate">{transaction.customerName}</div>
                </div>
                {transaction.customerPhone && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Telefone</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground">{transaction.customerPhone}</div>
                  </div>
                )}
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground dark:text-muted-foreground">E-mail</div>
                  <div className="text-sm font-medium text-foreground dark:text-foreground max-w-[60%] truncate" title={transaction.customerEmail}>{transaction.customerEmail}</div>
                </div>
                {transaction.customerDocument && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground dark:text-muted-foreground">Documento</div>
                    <div className="text-sm font-medium text-foreground dark:text-foreground">{transaction.customerDocument}</div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Rodapé com botões de ação */}
          <div className="p-4 sm:p-5 border-t border-border dark:border-border">
            <div className="flex flex-wrap justify-end gap-2">
              <Button
                variant="outline"
                onClick={onClose}
                className="text-foreground dark:text-foreground border-border dark:border-border hover:bg-accent/50 dark:hover:bg-accent/30"
              >
                Fechar
              </Button>
              {transaction.status === "PENDING" && (
                <Button
                  className="bg-highlight hover:bg-highlight/90 text-highlight-foreground gap-2"
                  onClick={checkPaymentStatus}
                  disabled={isCheckingPayment}
                >
                  {isCheckingPayment ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Verificando...
                    </>
                  ) : (
                    <>
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 16.2L4.8 12L3.4 13.4L9 19L21 7L19.6 5.6L9 16.2Z" fill="currentColor" />
                      </svg>
                      Verificar Pagamento
                    </>
                  )}
                </Button>
              )}
              {(transaction.pixCode || transaction.paymentDetails?.pixPayload) && transaction.status === "PENDING" && (
                <Button
                  className="bg-primary hover:bg-primary/90 text-primary-foreground gap-2"
                  onClick={() => copyToClipboard(transaction.pixCode || transaction.paymentDetails?.pixPayload || "")}
                >
                  <Copy className="h-4 w-4" />
                  Copiar e Pagar com PIX
                </Button>
              )}
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
