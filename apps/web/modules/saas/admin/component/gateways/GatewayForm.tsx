"use client";

import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { useRouter } from "@shared/hooks/router";
import { useQueryClient } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
// import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@ui/components/select";
import { Switch } from "@ui/components/switch";
import { Spinner } from "@shared/components/Spinner";
import { getAdminPath } from "@saas/admin/lib/links";
import { adminGatewaysQueryKey, useAdminGatewayQuery, useAdminOrganizationsQuery } from "@saas/admin/lib/api";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@ui/components/form";
import { useToast } from "@ui/hooks/use-toast";

const gatewayTypes = [
  { id: "FLOW2PAY", name: "Flow2Pay" },
  { id: "REFLOWPAY", name: "ReflowPay" },
  { id: "PRIMEPAG", name: "PrimePag" },
  { id: "PIXIUM", name: "Pixium" },
  { id: "TRANSFEERA", name: "Transfeera" },
  { id: "PLUGGOU_PIX", name: "Pluggou PIX" },
];

const formSchema = z.object({
  type: z.string().min(1, "Tipo é obrigatório"),
  isActive: z.boolean().default(true),
  isDefault: z.boolean().default(false),
  priority: z.coerce.number().int().min(1).default(999),
  credentials: z.record(z.any()),
  canReceive: z.boolean().default(true),
  canSend: z.boolean().default(false),
  pixChargePercentFee: z.coerce.number().min(0).default(0),
  pixTransferPercentFee: z.coerce.number().min(0).default(0),
  pixChargeFixedFee: z.coerce.number().min(0).default(0),
  pixTransferFixedFee: z.coerce.number().min(0).default(0),
});

type FormValues = z.infer<typeof formSchema>;

export function GatewayForm({ gatewayId }: { gatewayId?: string }) {
  const t = useTranslations();
  const router = useRouter();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedType, setSelectedType] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { data: gateway, isLoading: isLoadingGateway } = useAdminGatewayQuery(
    gatewayId || "",
    { enabled: !!gatewayId }
  );

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      type: "",
      isActive: true,
      isDefault: false,
      priority: 999,
      credentials: {},
      canReceive: true,
      canSend: false,
      pixChargePercentFee: 0,
      pixTransferPercentFee: 0,
      pixChargeFixedFee: 0,
      pixTransferFixedFee: 0,
    },
  });

  useEffect(() => {
    if (gateway) {
      form.reset({
        type: gateway.type,
        isActive: gateway.isActive,
        isDefault: gateway.isDefault,
        priority: gateway.priority || 999,
        credentials: gateway.credentials as Record<string, any>,
        canReceive: gateway.canReceive || true,
        canSend: gateway.canSend || false,
        pixChargePercentFee: gateway.pixChargePercentFee || 0,
        pixTransferPercentFee: gateway.pixTransferPercentFee || 0,
        pixChargeFixedFee: gateway.pixChargeFixedFee || 0,
        pixTransferFixedFee: gateway.pixTransferFixedFee || 0,
      });
      setSelectedType(gateway.type);
    }
  }, [gateway, form]);

  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    try {
      // Get gateway name from selected type
      const selectedGateway = gatewayTypes.find(g => g.id === values.type);
      const gatewayName = selectedGateway?.name || values.type;

      // Add name to the values
      const submissionValues = {
        ...values,
        name: gatewayName
      };

      const url = gatewayId && gatewayId !== "new"
        ? `/api/admin/gateways/${gatewayId}`
        : `/api/admin/gateways`;

      const method = gatewayId && gatewayId !== "new" ? "PATCH" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(submissionValues),
      });

      if (!response.ok) {
        throw new Error("Failed to save gateway");
      }

      toast({
        variant: "success",
        title: "Gateway salvo com sucesso",
      });

      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: adminGatewaysQueryKey,
      });

      router.push(getAdminPath("/gateways"));
    } catch (error) {
      toast({
        variant: "error",
        title: "Erro ao salvar gateway",
        description: error instanceof Error ? error.message : String(error),
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderCredentialsFields = () => {
    switch (selectedType) {
      case "ASAAS":
        return (
          <>
            <FormField
              control={form.control}
              name="credentials.apiKey"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>API Key</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Chave API do Asaas" />
                  </FormControl>
                  <FormDescription>
                    Chave de API fornecida pelo Asaas
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="credentials.environment"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ambiente</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o ambiente" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="sandbox">Sandbox</SelectItem>
                      <SelectItem value="production">Produção</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Ambiente de integração com o Asaas
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        );
      case "PIXIUM":
        return (
          <>
            <FormField
              control={form.control}
              name="credentials.apiKey"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>API Key</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="sk_like_XxIoh02BaPATFPfeweeNaxkos9J4wAIEWUpzp9W7YxcG7G8j" />
                  </FormControl>
                  <FormDescription>
                    Chave secreta do Pixium
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="credentials.companyId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Company ID</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="655d3bad-726b-4538-8a1e-9ea053cb7d9a" />
                  </FormControl>
                  <FormDescription>
                    ID da empresa no Pixium
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="credentials.environment"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ambiente</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o ambiente" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="sandbox">Sandbox</SelectItem>
                      <SelectItem value="production">Produção</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Ambiente de integração com o Pixium
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        );
      case "TRANSFEERA":
        return (
          <>
            <FormField
              control={form.control}
              name="credentials.clientId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Client ID</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="c7c34614-8202-491f-a059-86d3b05229d8" />
                  </FormControl>
                  <FormDescription>
                    Client ID do Transfeera
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="credentials.clientSecret"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Client Secret</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="ac7ba99b-30bd-4c03-9845-48ce354cebed50ce3b91-8ed4-46b8-a8d1-240d7827a130" />
                  </FormControl>
                  <FormDescription>
                    Client Secret do Transfeera
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="credentials.environment"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ambiente</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o ambiente" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="sandbox">Sandbox</SelectItem>
                      <SelectItem value="production">Produção</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Ambiente de integração
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        );
      case "REFLOWPAY":
        return (
          <>
            <FormField
              control={form.control}
              name="credentials.secretKey"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Secret Key</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="sk_..." />
                  </FormControl>
                  <FormDescription>
                    Chave secreta do ReflowPay
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="credentials.publicKey"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Public Key</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="pk_..." />
                  </FormControl>
                  <FormDescription>
                    Chave pública do ReflowPay
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        );
      case "PRIMEPAG":
        return (
          <>
            <FormField
              control={form.control}
              name="credentials.clientId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Client ID</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="client_id..." />
                  </FormControl>
                  <FormDescription>
                    Client ID do PrimePag
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="credentials.clientSecret"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Client Secret</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="client_secret..." />
                  </FormControl>
                  <FormDescription>
                    Client Secret do PrimePag
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        );
      default:
        return (
          <FormField
            control={form.control}
            name="credentials"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("admin.gateways.form.credentialsJson")}</FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    value={typeof field.value === 'object' ? JSON.stringify(field.value, null, 2) : field.value}
                    onChange={(e) => {
                      try {
                        const parsed = JSON.parse(e.target.value);
                        field.onChange(parsed);
                      } catch {
                        field.onChange(e.target.value);
                      }
                    }}
                    placeholder="{}"
                    rows={10}
                  />
                </FormControl>
                <FormDescription>
                  {t("admin.gateways.form.credentialsJsonDescription")}
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        );
    }
  };

  if (isLoadingGateway && gatewayId && gatewayId !== "new") {
    return (
      <div className="flex items-center justify-center p-12">
        <Spinner />
      </div>
    );
  }

  // Determine page title
  const pageTitle = gatewayId && gatewayId !== "new"
    ? "Editar Gateway de Pagamento"
    : "Adicionar Gateway de Pagamento";

  return (
    <Card>
      <CardHeader>
        <CardTitle>{pageTitle}</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 gap-6">
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tipo de Gateway</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                        setSelectedType(value);
                      }}
                      value={field.value}
                      disabled={!!gatewayId && gatewayId !== "new"}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o tipo" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {gatewayTypes.map((type) => (
                          <SelectItem key={type.id} value={type.id}>
                            {type.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      O tipo de gateway determina a integração que será utilizada.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="isActive"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Ativo</FormLabel>
                        <FormDescription>
                          Determina se este gateway está ativo e pode ser usado
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="isDefault"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Padrão</FormLabel>
                        <FormDescription>
                          Define este gateway como o padrão para a empresa
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="canReceive"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Pode Receber Pagamentos</FormLabel>
                        <FormDescription>
                          Define se este gateway pode receber pagamentos (PIX, etc.)
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="canSend"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Pode Enviar Pagamentos</FormLabel>
                        <FormDescription>
                          Define se este gateway pode enviar pagamentos (transferências PIX, etc.)
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Prioridade</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormDescription>
                      Prioridade do gateway (menor número = maior prioridade)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">Taxas para Cobranças PIX</h3>
                  <div className="space-y-3">
                    <FormField
                      control={form.control}
                      name="pixChargePercentFee"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Taxa Percentual (%)</FormLabel>
                          <FormControl>
                            <Input type="number" step="0.01" {...field} />
                          </FormControl>
                          <FormDescription>
                            Taxa percentual cobrada para recebimentos PIX (Ex: 1.5 para 1,5%)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="pixChargeFixedFee"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Taxa Fixa (centavos)</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormDescription>
                            Taxa fixa em centavos cobrada para recebimentos PIX (Ex: 50 para R$0,50)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-3">Taxas para Transferências PIX</h3>
                  <div className="space-y-3">
                    <FormField
                      control={form.control}
                      name="pixTransferPercentFee"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Taxa Percentual (%)</FormLabel>
                          <FormControl>
                            <Input type="number" step="0.01" {...field} />
                          </FormControl>
                          <FormDescription>
                            Taxa percentual cobrada para transferências PIX (Ex: 1.5 para 1,5%)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="pixTransferFixedFee"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Taxa Fixa (centavos)</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormDescription>
                            Taxa fixa em centavos cobrada para transferências PIX (Ex: 50 para R$0,50)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>

              {selectedType && (
                <div>
                  <h3 className="text-lg font-medium mb-3">Credenciais</h3>
                  <div className="space-y-3">
                    {renderCredentialsFields()}
                  </div>
                </div>
              )}
            </div>

            <div className="flex justify-end space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push(getAdminPath("/gateways"))}
                disabled={isSubmitting}
              >
                Cancelar
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Spinner className="mr-2 h-4 w-4" />}
                Salvar
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
