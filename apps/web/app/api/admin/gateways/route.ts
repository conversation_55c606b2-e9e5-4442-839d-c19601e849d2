import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { NextRequest, NextResponse } from "next/server";
import { Zod<PERSON>rror, z } from "zod";
import { auth } from "@repo/auth";
import { headers } from "next/headers";

// Schema for validating gateway data
const gatewaySchema = z.object({
  type: z.string(),
  isActive: z.boolean().default(false),
  isDefault: z.boolean().default(false),
  priority: z.coerce.number().int().min(0).default(999),
  credentials: z.record(z.any()).default({}),
  canReceive: z.boolean().default(true),
  canSend: z.boolean().default(false),
  pixChargePercentFee: z.coerce.number().min(0).max(100).default(0),
  pixTransferPercentFee: z.coerce.number().min(0).max(100).default(0),
  pixChargeFixedFee: z.coerce.number().min(0).default(0),
  pixTransferFixedFee: z.coerce.number().min(0).default(0),
});

export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user is an admin
    const userData = await db.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (userData?.role !== "admin") {
      return NextResponse.json(
        { error: "Forbidden. Admin access required." },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const query = searchParams.get("query") || "";
    const limit = parseInt(searchParams.get("limit") || "10", 10);
    const offset = parseInt(searchParams.get("offset") || "0", 10);
    const organizationId = searchParams.get("organizationId") || "global";

    // Build where clause
    const whereClause: any = {};

    if (query) {
      whereClause.name = { contains: query, mode: "insensitive" };
    }

    // Get global or specific organization gateways
    if (organizationId !== "global") {
      // Para gateways específicos de uma organização, usamos a relação many-to-many
      whereClause.OR = [
        {
          organizations: {
            some: {
              organizationId
            }
          }
        },
        { isGlobal: true }
      ];
    } else {
      // Apenas gateways globais
      whereClause.isGlobal = true;
    }

    // Get gateways
    const gateways = await db.paymentGateway.findMany({
      where: whereClause,
      take: limit,
      skip: offset,
      orderBy: [
        { isDefault: 'desc' },
        { priority: 'asc' },
      ],
    });

    // Count total
    const total = await db.paymentGateway.count({
      where: whereClause,
    });

    return NextResponse.json({ gateways, total });
  } catch (error) {
    console.error("Error fetching gateways:", error);
    return NextResponse.json(
      { error: "Failed to fetch gateways" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verificar autenticação do usuário
    const session = await getSession();
    if (!session?.user || !session.user.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Verificar se o usuário é admin
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (user?.role !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized: Admin access required" },
        { status: 403 }
      );
    }

    // Receber e validar os dados do gateway
    const body = await request.json();
    const validationResult = gatewaySchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Dados inválidos", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const gatewayData = validationResult.data;

    // Validações específicas por tipo de gateway
    if (gatewayData.type.toLowerCase() === 'mediuspag') {
      // Verificar se companyId está presente nas credenciais para MediusPag
      if (!gatewayData.credentials.companyId) {
        return NextResponse.json(
          { error: "O campo Company ID é obrigatório para o gateway MediusPag" },
          { status: 400 }
        );
      }
    }

    // Se isDefault for true, desmarcar outros gateways como padrão
    if (gatewayData.isDefault) {
      await db.paymentGateway.updateMany({
        where: {
          isDefault: true,
        },
        data: {
          isDefault: false,
        },
      });
    }

    // Criar o gateway
    const gateway = await db.paymentGateway.create({
      data: {
        name: getGatewayName(gatewayData.type),
        type: gatewayData.type,
        isActive: gatewayData.isActive,
        isDefault: gatewayData.isDefault,
        priority: gatewayData.priority,
        credentials: gatewayData.credentials,
        canReceive: gatewayData.canReceive,
        canSend: gatewayData.canSend,
        configuredById: session.user.id,
        isGlobal: true, // Definindo como gateway global
      },
    });

    return NextResponse.json(gateway, { status: 201 });
  } catch (error) {
    logger.error("Error creating gateway:", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });

    // Mensagens de erro mais específicas
    if (error instanceof Error) {
      if (error.message.includes("organizationId")) {
        return NextResponse.json(
          { error: "Erro no campo organizationId. Este campo pode ter sido removido ou alterado no esquema." },
          { status: 500 }
        );
      }

      if (error.message.includes("credentials")) {
        return NextResponse.json(
          { error: "Erro nas credenciais do gateway. Verifique se todos os campos obrigatórios foram fornecidos." },
          { status: 500 }
        );
      }
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Erro interno ao criar o gateway" },
      { status: 500 }
    );
  }
}

// Função auxiliar para obter o nome do gateway com base no tipo
function getGatewayName(type: string): string {
  const gatewayTypes: Record<string, string> = {
    flow2pay: "Flow2Pay",
    mercadopago: "Mercado Pago",
    asaas: "Asaas",
    reflowpay: "ReflowPay",
    primepag: "PrimePag",
    pixium: "Pixium",
    transfeera: "Transfeera",
    mediuspag: "MediusPag",
    pluggou_pix: "Pluggou PIX",
  };

  return gatewayTypes[type.toLowerCase()] || type;
}
