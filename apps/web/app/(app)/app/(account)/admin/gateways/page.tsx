import { PageHeader } from "@saas/shared/components/PageHeader";
import { Card, CardContent } from "@ui/components/card";
import { db } from "@repo/database";
import { Button } from "@ui/components/button";
import { PlusIcon, Info, AlertCircle } from "lucide-react";
import { Tabs, Tabs<PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@ui/components/tabs";
import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";
import { GatewayCardWrapper } from "./components/GatewayCardWrapper";

export const metadata = {
  title: "Administração de Gateways",
};

// Available gateway types with display info
const availableGateways = [
  {
    id: "flow2pay",
    name: "Flow2Pay",
    description: "Integração direta com Flow2Pay para PIX com performance otimizada.",
    logo: "/images/gateways/flow2pay.png",
    features: {
      pixReceive: true,
      pixSend: true
    },
    highlight: true
  },
  {
    id: "pluggou_pix",
    name: "Pluggou PIX",
    description: "Gateway de pagamentos PIX nativo da Pluggou.",
    logo: "/images/gateways/pluggou.png",
    features: {
      pixReceive: true,
      pixSend: true
    },
    highlight: false
  },
  {
    id: "reflowpay",
    name: "ReflowPay",
    description: "Processador de pagamentos com foco em Pix e métodos alternativos.",
    logo: "/images/gateways/reflowpay.png",
    features: {
      pixReceive: true,
      pixSend: false
    },
    highlight: false
  },
  {
    id: "primepag",
    name: "PrimePag",
    description: "Adquirente de pagamentos completo para e-commerce e marketplaces.",
    logo: "/images/gateways/primepag.png",
    features: {
      pixReceive: true,
      pixSend: false
    },
    highlight: false
  },
  {
    id: "pixium",
    name: "Pixium",
    description: "Plataforma especializada em pagamentos Pix para empresas de todos os portes.",
    logo: "/images/gateways/pixium.png",
    features: {
      pixReceive: true,
      pixSend: false
    },
    highlight: false
  },
  {
    id: "transfeera",
    name: "Transfeera",
    description: "Solução completa para transferências e pagamentos via Pix.",
    logo: "/images/gateways/transfeera.png",
    features: {
      pixReceive: true,
      pixSend: true
    },
    highlight: false
  },
  {
    id: "mediuspag",
    name: "MediusPag",
    description: "Plataforma de pagamentos com suporte a Pix e outros métodos.",
    logo: "/images/gateways/mediuspag.png",
    features: {
      pixReceive: true,
      pixSend: false
    },
    highlight: false
  }
];

// Definir tipo para gateway com todas as propriedades esperadas
interface PaymentGatewayWithFees {
  id: string;
  type: string;
  name: string;
  isActive: boolean;
  isDefault: boolean;
  priority: number;
  credentials: any;
  canReceive: boolean;
  canSend: boolean;
  createdAt: Date;
  updatedAt: Date;
  configuredById: string | null;
}

export default async function AdminGatewaysPage() {
  try {
    // Get all gateways from database
    const configuredGateways = await db.paymentGateway.findMany({
      orderBy: [
        { priority: 'asc' },
        { createdAt: 'desc' },
      ],
      select: {
        id: true,
        type: true,
        name: true,
        credentials: true,
        isActive: true,
        isDefault: true,
        priority: true,
        canReceive: true,
        canSend: true,
        configuredById: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    // Map the configured gateways for easier lookup
    const configuredGatewayMap: Record<string, any> = {};

    configuredGateways.forEach((gateway: PaymentGatewayWithFees) => {
      configuredGatewayMap[gateway.type.toLowerCase()] = {
        id: gateway.id,
        isActive: gateway.isActive,
        isDefault: gateway.isDefault,
        priority: gateway.priority || 999,
        canReceive: gateway.canReceive ?? true,
        canSend: gateway.canSend ?? false,
        pixChargePercentFee: 0,
        pixTransferPercentFee: 0,
        pixChargeFixedFee: 0,
        pixTransferFixedFee: 0,
        credentials: gateway.credentials,
      };
    });

    // Categorize gateways based on configuration status
    const activeGateways = availableGateways.filter(gateway =>
      configuredGatewayMap[gateway.id.toLowerCase()]?.isActive === true
    );

    const inactiveGateways = availableGateways.filter(gateway =>
      configuredGatewayMap[gateway.id.toLowerCase()]?.isActive === false
    );

    const unconfiguredGateways = availableGateways.filter(gateway =>
      configuredGatewayMap[gateway.id.toLowerCase()] === undefined
    );

    // Check if there's at least one default gateway
    const hasDefaultGateway = configuredGateways.some(gateway => gateway.isDefault && gateway.isActive);
    const activeGatewaysCount = configuredGateways.filter(gateway => gateway.isActive).length;

    return (
      <div className="space-y-6">
        <PageHeader
          title="Gateways de Pagamento"
          subtitle="Administre todos os gateways de pagamento disponíveis no sistema"
        />

        {!hasDefaultGateway && activeGatewaysCount > 0 && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Nenhum gateway padrão definido</AlertTitle>
            <AlertDescription>
              Existem gateways ativos, mas nenhum está definido como padrão. É recomendável definir pelo menos um gateway padrão.
            </AlertDescription>
          </Alert>
        )}

        {activeGatewaysCount === 0 && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>Nenhum gateway ativo</AlertTitle>
            <AlertDescription>
              Não há gateways ativos no sistema. Configure e ative pelo menos um gateway para permitir transações.
            </AlertDescription>
          </Alert>
        )}

        <Tabs defaultValue="all" className="w-full">
          <div className="flex items-center justify-between mb-6">
            <TabsList>
              <TabsTrigger value="all">Todos ({availableGateways.length})</TabsTrigger>
              <TabsTrigger value="active">Ativos ({activeGateways.length})</TabsTrigger>
              <TabsTrigger value="inactive">Inativos ({inactiveGateways.length})</TabsTrigger>
              <TabsTrigger value="available">Disponíveis ({unconfiguredGateways.length})</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="all" className="space-y-6">
            {activeGateways.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Gateways Ativos</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {activeGateways.map((gateway) => (
                    <GatewayCardWrapper
                      key={gateway.id}
                      gateway={gateway}
                      gatewayData={configuredGatewayMap[gateway.id.toLowerCase()]}
                      isConfigured={true}
                      isActive={true}
                    />
                  ))}
                </div>
              </div>
            )}

            {inactiveGateways.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Gateways Inativos</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {inactiveGateways.map((gateway) => (
                    <GatewayCardWrapper
                      key={gateway.id}
                      gateway={gateway}
                      gatewayData={configuredGatewayMap[gateway.id.toLowerCase()]}
                      isConfigured={true}
                      isActive={false}
                    />
                  ))}
                </div>
              </div>
            )}

            {unconfiguredGateways.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Gateways Disponíveis</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {unconfiguredGateways.map((gateway) => (
                    <GatewayCardWrapper
                      key={gateway.id}
                      gateway={gateway}
                      isConfigured={false}
                      isActive={false}
                    />
                  ))}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="active">
            {activeGateways.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {activeGateways.map((gateway) => (
                  <GatewayCardWrapper
                    key={gateway.id}
                    gateway={gateway}
                    gatewayData={configuredGatewayMap[gateway.id.toLowerCase()]}
                    isConfigured={true}
                    isActive={true}
                  />
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="p-6 text-center">
                  <p className="text-muted-foreground">Nenhum gateway ativo configurado.</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="inactive">
            {inactiveGateways.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {inactiveGateways.map((gateway) => (
                  <GatewayCardWrapper
                    key={gateway.id}
                    gateway={gateway}
                    gatewayData={configuredGatewayMap[gateway.id.toLowerCase()]}
                    isConfigured={true}
                    isActive={false}
                  />
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="p-6 text-center">
                  <p className="text-muted-foreground">Nenhum gateway inativo configurado.</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="available">
            {unconfiguredGateways.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {unconfiguredGateways.map((gateway) => (
                  <GatewayCardWrapper
                    key={gateway.id}
                    gateway={gateway}
                    isConfigured={false}
                    isActive={false}
                  />
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="p-6 text-center">
                  <p className="text-muted-foreground">Todos os gateways disponíveis já foram configurados.</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    );
  } catch (error) {
    console.error("Error fetching payment gateways:", error instanceof Error ? error.message : "Unknown error");
    return (
      <div className="space-y-6">
        <PageHeader
          title="Gateways de Pagamento"
          subtitle="Administre todos os gateways de pagamento disponíveis no sistema"
        />
        <Card className="p-6">
          <div className="flex h-64 items-center justify-center text-foreground/60">
            Erro ao carregar os gateways. Por favor, tente novamente mais tarde.
          </div>
        </Card>
      </div>
    );
  }
}
