# ==============================================================================
# Base Stage - Configura Node.js e PNPM
# ==============================================================================
FROM node:22-slim AS base
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
# Habilita corepack para gerenciar o pnpm
RUN corepack enable

# ==============================================================================
# Builder Stage - Instala dependências e faz o "prune" do projeto
# ==============================================================================
FROM base AS builder
# Instala o turbo globalmente
RUN pnpm add -g turbo
# Define o diretório de trabalho
WORKDIR /app
# Copia os arquivos do projeto
COPY . .
# Cria uma versão "pruned" do monorepo focada na app 'web' para otimizar
# as camadas do Docker e a instalação de dependências.
RUN turbo prune @repo/web --docker

# ==============================================================================
# Installer Stage - Instala dependências e constrói a aplicação
# ==============================================================================
FROM base AS installer
WORKDIR /app

# Define opções do Node.js para AUMENTAR A MEMÓRIA HEAP.
# Comece com 4096 (4GB). Se o erro persistir e seu servidor tiver mais RAM,
# tente 6144 (6GB) ou 8192 (8GB).
# '--optimize-for-size' instrui o V8 a priorizar memória em vez de velocidade.
ENV NODE_OPTIONS="--max-old-space-size=4096 --optimize-for-size"

# Copia os arquivos de lock e package.json "pruned"
COPY --from=builder /app/out/json/ .
COPY --from=builder /app/out/pnpm-lock.yaml ./pnpm-lock.yaml

# Instala SOMENTE as dependências de produção (e dev se necessário para build)
# '--frozen-lockfile' é recomendado para CI/Docker para garantir consistência.
# CYPRESS_INSTALL_BINARY=0 evita baixar o binário do Cypress se for dev dep.
ENV CYPRESS_INSTALL_BINARY=0
RUN pnpm install --frozen-lockfile

# Copia o código-fonte "pruned" completo
COPY --from=builder /app/out/full/ .
# Copia o turbo.json
COPY turbo.json turbo.json

# Constrói a aplicação 'web' e suas dependências internas.
# O NODE_OPTIONS definido anteriormente será usado por este comando.
# Lembre-se de otimizar seu next.config.js (webpackMemoryOptimizations, etc.)
# e verificar sua versão do TypeScript (versões > 5.5.4 podem usar mais memória).
RUN pnpm turbo run build --filter=@repo/web...

# ==============================================================================
# Runner Stage - Configura e executa a aplicação final
# ==============================================================================
FROM base AS runner
WORKDIR /app

# Cria um grupo e usuário não-root para executar a aplicação (Boas Práticas de Segurança)
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
# Define o usuário 'nextjs' como padrão
USER nextjs

# Copia os arquivos essenciais para o runtime.
# ATENÇÃO: Os arquivos next.config.ts e package.json geralmente NÃO são necessários
# para um build 'standalone'. Verifique se sua aplicação realmente precisa deles
# em tempo de execução. Se não, remova estas duas linhas.
COPY --from=installer /app/apps/web/next.config.ts .
COPY --from=installer /app/apps/web/package.json .

# Copia a saída 'standalone' do Next.js, que contém o servidor e node_modules essenciais.
COPY --from=installer --chown=nextjs:nodejs /app/apps/web/.next/standalone ./
# Copia os assets estáticos.
COPY --from=installer --chown=nextjs:nodejs /app/apps/web/.next/static ./apps/web/.next/static
# Copia a pasta 'public'.
COPY --from=installer --chown=nextjs:nodejs /app/apps/web/public ./apps/web/public

# Expõe a porta que a aplicação usará.
EXPOSE 3000

# Define a porta e o hostname para o Next.js (ou sua aplicação).
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Comando para iniciar a aplicação a partir da saída 'standalone'.
# O Next.js 'standalone' geralmente coloca o server.js na raiz da pasta 'standalone'.
# O seu CMD original era ["node", "apps/web/server.js"]. Se o seu `standalone`
# mantém a estrutura 'apps/web', ele está correto. Caso contrário, ajuste para
# o caminho correto do server.js dentro da pasta 'standalone'.
CMD ["node", "apps/web/server.js"]
