datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-3.0.x"]
}

generator zod {
  provider         = "zod-prisma-types"
  output           = "../src/zod"
  createInputTypes = false
  addIncludeType   = false
  addSelectType    = false
}

model User {
  id                 String       @id
  name               String
  email              String
  emailVerified      Boolean
  image              String?
  createdAt          DateTime
  updatedAt          DateTime
  username           String?
  role               String?
  banned             Boolean?
  banReason          String?
  banExpires         DateTime?
  onboardingComplete Boolean      @default(false)
  locale             String?
  twoFactorEnabled   Boolean      @default(false)
  sessions           Session[]
  accounts           Account[]
  passkeys           Passkey[]
  invitations        Invitation[]
  purchases          Purchase[]
  memberships        Member[]
  twoFactor          TwoFactor?
  ApiKey             ApiKey[]

  @@unique([email])
  @@unique([username])
  @@map("user")
}

model Session {
  id        String   @id
  expiresAt DateTime
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  impersonatedBy String?

  activeOrganizationId String?

  token     String
  createdAt DateTime
  updatedAt DateTime

  @@unique([token])
  @@map("session")
}

model Account {
  id           String    @id
  accountId    String
  providerId   String
  userId       String
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken  String?
  refreshToken String?
  idToken      String?
  expiresAt    DateTime?
  password     String?

  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Verification {
  id         String   @id
  identifier String
  value      String
  expiresAt  DateTime

  createdAt DateTime?
  updatedAt DateTime?

  @@map("verification")
}

model Passkey {
  id             String    @id
  name           String?
  publicKey      String
  userId         String
  user           User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  webauthnUserID String
  counter        Int
  deviceType     String
  backedUp       Boolean
  transports     String?
  createdAt      DateTime?

  @@map("passkey")
}

enum OrganizationStatus {
  PENDING_REVIEW
  APPROVED
  REJECTED
  BLOCKED
}

model Organization {
  id                  String                 @id
  name                String
  slug                String?
  logo                String?
  createdAt           DateTime
  metadata            String?
  status              OrganizationStatus     @default(PENDING_REVIEW)
  members             Member[]
  invitations         Invitation[]
  purchases           Purchase[]
  transactions        Transaction[]
  pixKeys             PixKey[]
  webhooks            Webhook[]
  webhookEvents       WebhookEvent[]
  ApiKey              ApiKey[]
  legalInfo           OrganizationLegalInfo?
  taxes               OrganizationTaxes?
  gateways            OrganizationGateway[]
  OrganizationBalance OrganizationBalance?
  BalanceHistory      BalanceHistory[]
  documents           OrganizationDocument?
  pixWebhookEvents    PixWebhookEvent[]
  SvixAppChannel      SvixAppChannel?

  @@unique([slug])
  @@map("organization")
}

model Member {
  id             String       @id
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  userId         String
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  role           String
  createdAt      DateTime

  @@unique([userId, organizationId])
  @@map("member")
}

model Invitation {
  id             String       @id
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  email          String
  role           String?
  status         String
  expiresAt      DateTime
  inviterId      String
  user           User         @relation(fields: [inviterId], references: [id], onDelete: Cascade)

  @@map("invitation")
}

enum PurchaseType {
  SUBSCRIPTION
  ONE_TIME
}

model Purchase {
  id             String        @id @default(cuid())
  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String?
  user           User?         @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId         String?
  type           PurchaseType
  customerId     String
  subscriptionId String?       @unique
  productId      String?
  status         String?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt

  @@index([subscriptionId])
  @@map("purchase")
}

enum TransactionStatus {
  PENDING
  APPROVED
  REJECTED
  CANCELED
  PROCESSING
  REFUNDED
  BLOCKED
}

enum TransactionType {
  CHARGE // Cobrança via Pix
  SEND // Envio de Pix
  RECEIVE // Recebimento de Pix
  REFUND // Estorno de transação
}

enum PixKeyType {
  CPF
  CNPJ
  EMAIL
  PHONE
  RANDOM
}

model Transaction {
  id                    String            @id @default(cuid())
  externalId            String? // ID da transação no gateway
  referenceCode         String? // Código de referência para o cliente
  endToEndId            String? // PIX End-to-End ID (E2E ID)
  customerName          String
  customerEmail         String
  customerPhone         String?
  customerDocument      String?
  customerDocumentType  String?
  createdAt             DateTime          @default(now())
  paymentAt             DateTime?
  amount                Float
  status                TransactionStatus
  pixKey                String? // Chave Pix usada
  pixKeyType            PixKeyType?
  type                  TransactionType
  description           String?
  metadata              Json? // Dados adicionais da transação
  organizationId        String
  organization          Organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  gatewayId             String?
  gatewayName           String? // Nome do gateway utilizado
  gateway               PaymentGateway?   @relation(fields: [gatewayId], references: [id])
  updatedAt             DateTime          @updatedAt
  processedAt           DateTime? // Data de processamento (útil para estornos)
  reason                String? // Motivo (para estornos)
  originalTransactionId String?
  originalTransaction   Transaction?      @relation("RefundTransactions", fields: [originalTransactionId], references: [id])
  refunds               Transaction[]     @relation("RefundTransactions")
  blocks                CautionaryBlock[]
  webhookEvents         WebhookEvent[]
  balanceHistory        BalanceHistory[]
  pixWebhookEvents      PixWebhookEvent[]

  // Fee information
  percentFee Float  @default(0) // Taxa percentual aplicada
  fixedFee   Float  @default(0) // Taxa fixa aplicada
  totalFee   Float  @default(0) // Total de taxas (percentual + fixa)
  netAmount  Float? // Valor líquido após taxas

  @@index([externalId])
  @@index([endToEndId])
  @@index([organizationId])
  @@index([status])
  @@index([type])
  @@index([originalTransactionId])
  // Performance indexes for duplicate checking and queries
  @@index([referenceCode])
  @@index([customerEmail, amount, organizationId, type, createdAt])
  @@index([organizationId, status, type])
  @@index([customerEmail])
  @@index([organizationId, createdAt])
  @@map("transaction")
}

model CautionaryBlock {
  id            String      @id @default(cuid())
  reason        String
  status        String // ACTIVE, RELEASED
  transactionId String
  transaction   Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  createdAt     DateTime    @default(now())
  releasedAt    DateTime?
  updatedAt     DateTime    @updatedAt

  @@index([transactionId])
  @@map("cautionary_block")
}

model PaymentGateway {
  id             String        @id @default(cuid())
  name           String // Ex: ReflowPay
  type           String // REFLOWPAY, STRIPE, etc.
  credentials    Json // Credenciais (ex: API key)
  isActive       Boolean       @default(false)
  isDefault      Boolean       @default(false)
  priority       Int           @default(999) // Lower number = higher priority
  canReceive     Boolean       @default(true) // Pode receber pagamentos (PIX, etc)
  canSend        Boolean       @default(false) // Pode enviar pagamentos (transferências PIX, etc)
  configuredById String? // ID do usuário (admin) que configurou o gateway
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  transactions   Transaction[]

  // Taxas padrão para este gateway
  pixChargePercentFee   Float @default(0) // Taxa percentual para cobranças PIX
  pixTransferPercentFee Float @default(0) // Taxa percentual para transferências PIX
  pixChargeFixedFee     Float @default(0) // Taxa fixa para cobranças PIX (em centavos)
  pixTransferFixedFee   Float @default(0) // Taxa fixa para transferências PIX (em centavos)

  // Remove direct organization relationship and replace with many-to-many
  isGlobal      Boolean               @default(false) // Indicates if this is a global gateway
  organizations OrganizationGateway[]

  // Performance indexes for gateway selection
  @@index([isActive, canReceive, priority])
  @@index([isGlobal, isDefault, isActive, canReceive, priority])
  @@index([type, isActive, canReceive])
  @@map("payment_gateway")
}

// New join table for many-to-many relationship between organizations and gateways
model OrganizationGateway {
  id             String         @id @default(cuid())
  organizationId String
  organization   Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  gatewayId      String
  gateway        PaymentGateway @relation(fields: [gatewayId], references: [id], onDelete: Cascade)
  isDefault      Boolean        @default(false) // Whether this gateway is the default for this organization
  isActive       Boolean        @default(true) // Whether this gateway is active for this organization
  priority       Int            @default(999) // Priority for this organization
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt

  @@unique([organizationId, gatewayId])
  @@index([organizationId])
  @@index([gatewayId])
  // Performance indexes for organization gateway selection
  @@index([organizationId, isActive, isDefault])
  @@index([organizationId, isActive, priority])
  @@map("organization_gateway")
}

model PixKey {
  id             String       @id @default(cuid())
  key            String
  type           PixKeyType
  description    String?
  isDefault      Boolean      @default(false)
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  @@index([organizationId])
  @@map("pix_key")
}

model TwoFactor {
  id          String    @id
  userId      String    @unique
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  secret      String
  backupCodes String?
  createdAt   DateTime? @default(now())
  updatedAt   DateTime? @updatedAt

  @@index([userId])
  @@map("twoFactor")
}

model Webhook {
  id             String            @id @default(cuid())
  url            String
  secret         String?
  events         String[] // Array of event types to listen for
  isActive       Boolean           @default(true)
  organizationId String
  organization   Organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdAt      DateTime          @default(now())
  updatedAt      DateTime          @updatedAt
  deliveries     WebhookDelivery[]

  // SVIX integration
  svixEndpointId String? @unique // ID of the endpoint in SVIX
  useSvix        Boolean @default(false) // Whether to use SVIX for this webhook

  @@index([organizationId])
  @@map("webhook")
}

model WebhookEvent {
  id             String            @id @default(cuid())
  type           String // e.g., "transaction.created", "transaction.updated"
  payload        Json // The event data
  transactionId  String?
  transaction    Transaction?      @relation(fields: [transactionId], references: [id], onDelete: SetNull)
  organizationId String
  organization   Organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdAt      DateTime          @default(now())
  deliveries     WebhookDelivery[]

  // SVIX integration
  svixMessageId String? @unique // ID of the message in SVIX
  svixEventType String? // Type of event in SVIX format

  @@index([organizationId])
  @@index([type])
  @@index([transactionId])
  @@map("webhook_event")
}

enum WebhookDeliveryStatus {
  PENDING
  SUCCESS
  FAILED
  RETRYING
}

model WebhookDelivery {
  id            String                @id @default(cuid())
  webhookId     String
  webhook       Webhook               @relation(fields: [webhookId], references: [id], onDelete: Cascade)
  eventId       String
  event         WebhookEvent          @relation(fields: [eventId], references: [id], onDelete: Cascade)
  status        WebhookDeliveryStatus
  attempts      Int                   @default(0)
  maxAttempts   Int                   @default(5)
  nextAttemptAt DateTime?
  lastAttemptAt DateTime?
  response      Json? // Response from the webhook endpoint
  error         String?
  createdAt     DateTime              @default(now())
  updatedAt     DateTime              @updatedAt

  // SVIX integration
  svixAttemptId String? @unique // ID of the attempt in SVIX

  @@index([webhookId])
  @@index([eventId])
  @@index([status])
  @@map("webhook_delivery")
}

// SVIX Configuration
model SvixConfig {
  id        String   @id @default(cuid())
  appId     String   @unique // SVIX App ID
  appName   String // Name of the SVIX app
  enabled   Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("svix_config")
}

// Modelo para controle de idempotência
model IdempotencyKey {
  key         String    @id
  operation   String // Nome da operação (ex: "createTransaction")
  status      String // "PENDING", "COMPLETED", "FAILED"
  result      Json? // Resultado da operação
  createdAt   DateTime  @default(now())
  completedAt DateTime?
  expiresAt   DateTime // Quando a chave expira

  @@index([operation])
  @@index([status])
  @@index([expiresAt])
  @@map("idempotency_key")
}

model ApiKey {
  id             String       @id @default(cuid())
  name           String
  type           String // "production" | "test"
  prefix         String       @unique
  hash           String
  permissions    Json
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdById    String
  createdBy      User         @relation(fields: [createdById], references: [id], onDelete: Cascade)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  lastUsedAt     DateTime?

  @@index([organizationId])
  @@index([createdById])
  @@map("api_key")
}

model OrganizationTaxes {
  id             String       @id @default(cuid())
  organizationId String       @unique
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  // Taxas PIX (percentuais)
  pixChargePercentFee   Float @default(0) // Taxa percentual para cobranças PIX
  pixTransferPercentFee Float @default(0) // Taxa percentual para transferências PIX

  // Taxas PIX (fixas)
  pixChargeFixedFee   Float @default(0) // Taxa fixa para cobranças PIX (em centavos)
  pixTransferFixedFee Float @default(0) // Taxa fixa para transferências PIX (em centavos)

  // Taxas por gateway (opcional - para casos específicos)
  gatewaySpecificTaxes Json? // Configurações específicas por gateway

  // Datas
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([organizationId])
  @@map("organization_taxes")
}

model OrganizationLegalInfo {
  id             String       @id @default(cuid())
  organizationId String       @unique
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  // Dados legais
  companyName            String
  tradingName            String?
  document               String // CNPJ
  documentType           String  @default("CNPJ")
  legalRepresentative    String
  legalRepDocumentNumber String // CPF

  // Endereço
  address    String
  city       String
  state      String
  postalCode String

  // Contato
  contactEmail String
  contactPhone String

  // Documentos
  documentsUploaded Json? // URLs para documentos enviados

  // Revisão
  reviewNotes String?
  reviewedBy  String? // ID do admin que revisou
  reviewedAt  DateTime?

  // Datas
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([organizationId])
  @@map("organization_legal_info")
}

model OrganizationBalance {
  id             String       @id @default(cuid())
  organizationId String       @unique
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  availableBalance Float @default(0) // Saldo disponível para transferências
  pendingBalance   Float @default(0) // Saldo pendente (em processamento)
  reservedBalance  Float @default(0) // Saldo reservado (em hold)

  history   BalanceHistory[]
  updatedAt DateTime         @updatedAt

  @@index([organizationId])
  @@map("organization_balance")
}

model BalanceHistory {
  id             String               @id @default(cuid())
  organizationId String
  organization   Organization         @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  balanceId      String?
  balance        OrganizationBalance? @relation(fields: [balanceId], references: [id], onDelete: SetNull)

  transactionId String?
  transaction   Transaction? @relation(fields: [transactionId], references: [id], onDelete: SetNull)

  operation   String // CREDIT, DEBIT, PENDING, RELEASE, RESERVE, UNRESERVE
  amount      Float // Valor da operação
  description String? // Descrição da operação

  balanceAfterOperation Json // Snapshot do saldo após a operação

  createdAt DateTime @default(now())

  @@index([organizationId])
  @@index([transactionId])
  @@index([operation])
  @@map("balance_history")
}

enum DocumentReviewStatus {
  PENDING_REVIEW
  APPROVED
  REJECTED
  REQUIRES_ADDITIONAL_INFO
}

model OrganizationDocument {
  id                       String               @id @default(cuid())
  organizationId           String               @unique
  organization             Organization         @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  cnpjDocument             String? // Path to CNPJ document
  businessLicense          String? // Path to business license
  bankStatement            String? // Path to bank statement
  representativeIdDocument String? // Path to representative ID document
  proofOfAddress           String? // Path to proof of address
  additionalDocument       String? // Path to any additional document
  status                   DocumentReviewStatus @default(PENDING_REVIEW)
  reviewedAt               DateTime?
  reviewedById             String?
  reviewNotes              String?
  submittedById            String
  createdAt                DateTime             @default(now())
  updatedAt                DateTime             @updatedAt

  @@map("organization_documents")
}

// PIX Webhook Events
model PixWebhookEvent {
  id          String    @id @default(uuid())
  type        String // e.g., "pix.in", "pix.out", "pix.reversal"
  status      String // e.g., "pending", "completed", "failed"
  payload     Json // Raw webhook payload
  processedAt DateTime? // When the webhook was processed
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  transactionId  String?
  transaction    Transaction?         @relation(fields: [transactionId], references: [id])
  organizationId String
  organization   Organization         @relation(fields: [organizationId], references: [id])
  deliveries     PixWebhookDelivery[]

  @@index([organizationId])
  @@index([transactionId])
  @@index([type])
  @@index([status])
  @@map("pix_webhook_events")
}

// PIX Webhook Deliveries
model PixWebhookDelivery {
  id        String          @id @default(cuid())
  eventId   String
  event     PixWebhookEvent @relation(fields: [eventId], references: [id], onDelete: Cascade)
  success   Boolean
  response  Json?
  error     String?
  createdAt DateTime        @default(now())
  updatedAt DateTime        @updatedAt

  @@index([eventId])
  @@map("pix_webhook_deliveries")
}

// SVIX App Channel for organizations
model SvixAppChannel {
  id             String       @id @default(cuid())
  organizationId String       @unique
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  svixAppId      String
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  @@index([organizationId])
  @@map("svix_app_channel")
}

// Comentários finais do schema
