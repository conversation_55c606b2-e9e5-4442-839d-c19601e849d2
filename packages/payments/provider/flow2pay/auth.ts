import { logger } from "@repo/logs";
import { getGatewayCredentials } from "../factory";
import type { Flow2PayAuthRequest, Flow2PayAuthResponse, Flow2PayConfig } from "./types";

// Default Flow2Pay API configuration
const FLOW2PAY_API_BASE_URL = "https://pixv2.flow2pay.com.br";
const DEFAULT_TIMEOUT = 15000; // 15 seconds
const DEFAULT_RETRIES = 2;
const TOKEN_BUFFER_TIME = 300000; // 5 minutes buffer before token expiry

// Authentication cache
interface TokenCache {
  accessToken: string;
  expiresAt: number;
}

const tokenCache = new Map<string, TokenCache>();

/**
 * Get Flow2Pay credentials for an organization
 */
export async function getFlow2PayCredentials(organizationId: string): Promise<Flow2PayConfig> {
  try {
    // Get credentials from the gateway configuration system
    const credentials = await getGatewayCredentials(organizationId, "FLOW2PAY");

    // Extract Flow2Pay specific credentials
    const config: Flow2PayConfig = {
      clientId: credentials.clientId || credentials.FLOW2PAY_CLIENT_ID || process.env.FLOW2PAY_CLIENT_ID || "",
      clientSecret: credentials.clientSecret || credentials.FLOW2PAY_CLIENT_SECRET || process.env.FLOW2PAY_CLIENT_SECRET || "",
      eventToken: credentials.eventToken || credentials.FLOW2PAY_EVENT_TOKEN || process.env.FLOW2PAY_EVENT_TOKEN || "",
      apiUrl: credentials.apiUrl || credentials.FLOW2PAY_API_URL || process.env.FLOW2PAY_API_URL || FLOW2PAY_API_BASE_URL,
      timeout: credentials.timeout || DEFAULT_TIMEOUT,
      retries: credentials.retries || DEFAULT_RETRIES
    };

    // Validate required credentials
    if (!config.clientId || !config.clientSecret || !config.eventToken) {
      logger.error("Flow2Pay credentials validation failed", {
        organizationId,
        hasClientId: !!config.clientId,
        hasClientSecret: !!config.clientSecret,
        hasEventToken: !!config.eventToken,
        availableKeys: Object.keys(credentials)
      });
      throw new Error(`Flow2Pay credentials not properly configured for organization ${organizationId}`);
    }

    logger.debug("Flow2Pay credentials retrieved successfully", {
      organizationId,
      apiUrl: config.apiUrl,
      hasClientId: !!config.clientId,
      hasClientSecret: !!config.clientSecret,
      hasEventToken: !!config.eventToken
    });

    return config;
  } catch (error) {
    logger.error("Error getting Flow2Pay credentials", {
      error: error instanceof Error ? error.message : String(error),
      organizationId
    });
    throw new Error(`Failed to get Flow2Pay credentials for organization ${organizationId}: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Get a valid Flow2Pay access token with caching and retry logic
 */
export async function getFlow2PayAccessToken(organizationId: string): Promise<string> {
  const cacheKey = `flow2pay_${organizationId}`;
  const now = Date.now();

  // Check if we have a valid cached token
  const cached = tokenCache.get(cacheKey);
  if (cached && cached.expiresAt > now + TOKEN_BUFFER_TIME) {
    logger.debug("Using cached Flow2Pay access token", { organizationId });
    return cached.accessToken;
  }

  // Get fresh token
  const credentials = await getFlow2PayCredentials(organizationId);
  const token = await authenticateWithFlow2Pay(credentials, organizationId);

  // Cache the token (expires in 24 hours according to Flow2Pay docs)
  const expiresAt = now + (24 * 60 * 60 * 1000); // 24 hours
  tokenCache.set(cacheKey, {
    accessToken: token,
    expiresAt
  });

  logger.info("Flow2Pay access token refreshed", { organizationId });
  return token;
}

/**
 * Authenticate with Flow2Pay API and get access token
 */
async function authenticateWithFlow2Pay(config: Flow2PayConfig, organizationId: string): Promise<string> {
  const { clientId, clientSecret, apiUrl, timeout, retries } = config;

  logger.info("Authenticating with Flow2Pay", { organizationId });

  let lastError: Error | null = null;

  for (let attempt = 0; attempt <= (retries || DEFAULT_RETRIES); attempt++) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout || DEFAULT_TIMEOUT);

      try {
        const response = await fetch(`${apiUrl}/no-auth/autenticacao/v1/api/login`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            clientId,
            clientSecret,
          } as Flow2PayAuthRequest),
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorText = await response.text();
          logger.error("Flow2Pay authentication failed", {
            status: response.status,
            error: errorText,
            attempt: attempt + 1,
            organizationId
          });

          // Determine if we should retry based on status code
          if (attempt < (retries || DEFAULT_RETRIES) &&
              (response.status >= 500 || response.status === 429)) {
            // Exponential backoff
            const delay = Math.min(100 * Math.pow(2, attempt), 2000);
            logger.info("Retrying Flow2Pay authentication", {
              delay_ms: delay,
              attempt: attempt + 1,
              max_attempts: (retries || DEFAULT_RETRIES) + 1,
              organizationId
            });
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }

          throw new Error(`Flow2Pay authentication failed: ${response.status} ${errorText}`);
        }

        const data: Flow2PayAuthResponse = await response.json();

        if (!data.accessToken) {
          logger.error("Flow2Pay authentication response missing token", {
            data,
            organizationId
          });
          throw new Error("Invalid authentication response from Flow2Pay");
        }

        logger.info("Flow2Pay authentication successful", { organizationId });
        return data.accessToken;

      } finally {
        clearTimeout(timeoutId);
      }

    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));

      // Check if this was a timeout
      const isTimeout = error instanceof Error &&
        (error.name === "AbortError" || error.message.includes("timeout"));

      logger.error("Flow2Pay authentication error", {
        error: lastError.message,
        attempt: attempt + 1,
        is_timeout: isTimeout,
        organizationId
      });

      // Determine if we should retry
      if (attempt < (retries || DEFAULT_RETRIES)) {
        // Exponential backoff
        const delay = Math.min(100 * Math.pow(2, attempt), 2000);
        logger.info("Retrying Flow2Pay authentication", {
          delay_ms: delay,
          attempt: attempt + 1,
          max_attempts: (retries || DEFAULT_RETRIES) + 1,
          organizationId
        });
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }
    }
  }

  // If we got here, all retries failed
  throw lastError || new Error("Failed to authenticate with Flow2Pay after multiple attempts");
}

/**
 * Clear cached token for an organization (useful for testing or when credentials change)
 */
export function clearFlow2PayTokenCache(organizationId: string): void {
  const cacheKey = `flow2pay_${organizationId}`;
  tokenCache.delete(cacheKey);
  logger.info("Flow2Pay token cache cleared", { organizationId });
}

/**
 * Clear all cached tokens
 */
export function clearAllFlow2PayTokenCache(): void {
  tokenCache.clear();
  logger.info("All Flow2Pay token cache cleared");
}
