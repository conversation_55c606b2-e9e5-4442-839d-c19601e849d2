/**
 * Flow2Pay API Types and Interfaces
 * Based on Flow2Pay API documentation at https://pixv2.flow2pay.com.br
 */

// Authentication interfaces
export interface Flow2PayAuthRequest {
  clientId: string;
  clientSecret: string;
}

export interface Flow2PayAuthResponse {
  accessToken: string;
  expiresIn: number; // in seconds (24 hours = 86400)
}

// QR Code generation interfaces
export interface Flow2PayQRCodeRequest {
  txId?: string; // Optional, auto-generated if not provided (26-35 alphanumeric chars)
  informacaoAdicional?: string; // Additional information for QR Code
  valor: number; // Amount in centavos (cents)
  tempoExpiracao: number; // Expiration time in seconds
  comImagem?: boolean; // Whether to include base64 image
  numeroDocumento?: string; // CPF/CNPJ without special characters
}

export interface Flow2PayQRCodeResponse {
  txid: string;
  qrcode: string; // PIX payload string
  imagemQrcode?: string | object; // Base64 image or object containing image data
  valor: number;
  tempoExpiracao: number;
  informacaoAdicional?: string;
  numeroDocumento?: string;
}

// PIX transfer interfaces
export interface Flow2PayTransferRequest {
  idEnvio: string; // Idempotency key (1-36 characters)
  valor: number; // Amount in centavos
  chavePixDestino: string; // Destination PIX key
  descricao: string; // Transfer description
  numeroDocumento?: string; // CPF/CNPJ without special characters
}

export interface Flow2PayTransferResponse {
  idEnvio: string;
  status: string;
  message?: string;
  codigoTransacao?: string;
}

// Transaction query interfaces
export interface Flow2PayTransactionQuery {
  codigoTransacao?: string; // Transaction code
  idEnvio?: string; // Send ID
  endToEnd?: string; // End-to-end ID
}

export interface Flow2PayTransactionResponse {
  codigoTransacao: string;
  idEnvio?: string;
  endToEndId?: string;
  status: string;
  valor: number;
  descricao?: string;
  horario: string;
  chavePix?: string;
  recebedor?: {
    nome: string;
    codigoBanco: string;
    cpf_cnpj: string;
  };
  erro?: {
    origem?: string;
    motivo?: string;
    mensagem?: string;
  };
}

// PIX refund interfaces
export interface Flow2PayRefundRequest {
  endToEnd: string; // Original transaction end-to-end ID
  valor: number; // Amount to refund in centavos
  descricao?: string; // Refund description
}

export interface Flow2PayRefundResponse {
  endToEndId: string;
  status: string;
  valor: number;
  descricao?: string;
  horario: string;
}

// Balance interfaces
export interface Flow2PayBalanceResponse {
  saldo: number; // Balance in centavos
  moeda: string; // Currency (BRL)
  dataAtualizacao: string; // Last update timestamp
}

// Balance details interfaces
export interface Flow2PayBalanceDetailsQuery {
  dataInicio: string; // Start date (YYYY-MM-DD)
  dataFim?: string; // End date (YYYY-MM-DD)
  tipo?: 'credito' | 'debito'; // Transaction type filter
}

export interface Flow2PayBalanceDetailsResponse {
  saldo: number;
  resumo: {
    totalCredito: number;
    totalDebito: number;
    quantidadeCredito: number;
    quantidadeDebito: number;
  };
  periodo: {
    dataInicio: string;
    dataFim: string;
  };
}

// Transaction history interfaces
export interface Flow2PayHistoryQuery {
  dataInicio: string; // Start date (YYYY-MM-DD)
  dataFim?: string; // End date (YYYY-MM-DD)
  tipo?: 'credito' | 'debito'; // Transaction type filter
  pagina?: number; // Page number (default: 1)
  porPagina?: number; // Items per page (default: 100)
}

export interface Flow2PayHistoryResponse {
  transacoes: Array<{
    codigoTransacao: string;
    tipo: 'credito' | 'debito';
    valor: number;
    descricao: string;
    horario: string;
    status: string;
    endToEndId?: string;
    idEnvio?: string;
    chavePix?: string;
  }>;
  paginacao: {
    pagina: number;
    porPagina: number;
    total: number;
    totalPaginas: number;
  };
}

// Webhook event interfaces
export interface Flow2PayWebhookEvent {
  evento: 'PixIn' | 'PixOut' | 'PixInReversal' | 'PixOutReversalExternal';
  token: string; // Event token
  txid?: string; // For PixIn events
  idEnvio?: string; // For PixOut events
  endToEndId?: string;
  codigoTransacao: string;
  status: 'Sucesso' | 'Falha' | 'Em processamento' | 'Erro';
  chavePix?: string;
  valor: number; // Positive for credits, negative for debits
  horario: string; // ISO timestamp
  recebedor?: {
    nome: string;
    codigoBanco: string;
    cpf_cnpj: string;
  };
  erro?: {
    origem?: string;
    motivo?: string;
    mensagem?: string;
  };
}

// Error response interface
export interface Flow2PayErrorResponse {
  sucesso: boolean;
  mensagem: string;
  codigo?: string;
  detalhes?: any;
}

// API configuration interface
export interface Flow2PayConfig {
  clientId: string;
  clientSecret: string;
  eventToken: string;
  apiUrl?: string; // Default: https://pixv2.flow2pay.com.br
  timeout?: number; // Request timeout in milliseconds
  retries?: number; // Number of retry attempts
}

// Internal provider response interfaces (standardized)
export interface Flow2PayProviderResponse {
  success: boolean;
  transactionId?: string;
  externalId: string;
  status: string;
  amount?: number;
  message?: string;
  raw?: any;
  metadata?: Record<string, any>;
}

export interface Flow2PayQRCodeProviderResponse extends Flow2PayProviderResponse {
  qrCode: string;
  qrCodeImage?: string;
  expiration: string;
  txid: string;
  pix: {
    payload: string;
    encodedImage?: string;
    expirationDate: string;
    txid: string;
  };
}

export interface Flow2PayTransferProviderResponse extends Flow2PayProviderResponse {
  idEnvio: string;
  endToEndId?: string;
}

// Utility types
export type Flow2PayPixKeyType = 'CPF' | 'CNPJ' | 'EMAIL' | 'PHONE' | 'RANDOM';
export type Flow2PayTransactionStatus = 'pending' | 'completed' | 'failed' | 'processing' | 'reversed';
export type Flow2PayTransactionType = 'CHARGE' | 'SEND' | 'REFUND';
