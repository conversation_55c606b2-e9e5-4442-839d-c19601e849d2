import { logger } from "@repo/logs";
import { getFlow2PayAccessToken, getFlow2PayCredentials } from "./auth";
import {
  toCentavos,
  fromCentavos,
  generateTxId,
  generateIdEnvio,
  formatPix<PERSON>ey,
  validatePix<PERSON>ey,
  mapFlow2PayStatus,
  extractQRCodeImage,
  delay,
  calculateBackoffDelay
} from "./utils";
import type {
  Flow2PayConfig,
  Flow2PayQRCodeRequest,
  Flow2PayQRCodeResponse,
  Flow2PayTransferRequest,
  Flow2PayTransferResponse,
  Flow2PayTransactionQuery,
  Flow2PayTransactionResponse,
  Flow2PayRefundRequest,
  Flow2PayRefundResponse,
  Flow2PayBalanceResponse,
  Flow2PayQRCodeProviderResponse,
  Flow2PayTransferProviderResponse,
  Flow2PayProviderResponse,
  Flow2PayPixKeyType,
  Flow2PayErrorResponse
} from "./types";

// Default configuration
const DEFAULT_TIMEOUT = 15000; // 15 seconds
const DEFAULT_RETRIES = 2;

/**
 * Make an authenticated API call to Flow2Pay
 */
async function callFlow2PayAPI<T>(
  endpoint: string,
  method: string,
  organizationId: string,
  body?: any,
  retries: number = DEFAULT_RETRIES
): Promise<T> {
  const config = await getFlow2PayCredentials(organizationId);
  const { apiUrl, timeout, eventToken } = config;

  let lastError: Error | null = null;

  logger.info("Flow2Pay API request", {
    endpoint,
    method,
    organizationId,
    has_body: !!body
  });

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      // Get authentication token
      const accessToken = await getFlow2PayAccessToken(organizationId);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout || DEFAULT_TIMEOUT);

      try {
        const headers: Record<string, string> = {
          "Authorization": `Bearer ${accessToken}`,
          "Token": eventToken,
          "Content-Type": "application/json",
        };

        const options: RequestInit = {
          method,
          headers,
          signal: controller.signal,
        };

        if (body && (method === "POST" || method === "PUT")) {
          options.body = JSON.stringify(body);
        }

        const url = `${apiUrl}${endpoint}`;
        const response = await fetch(url, options);

        clearTimeout(timeoutId);

        if (!response.ok) {
          let errorData: any;
          try {
            errorData = await response.json();
          } catch (e) {
            errorData = await response.text();
          }

          logger.error("Flow2Pay API error", {
            status: response.status,
            url,
            method,
            error: errorData,
            attempt: attempt + 1,
            organizationId
          });

          // Handle specific error cases
          if (response.status === 401) {
            throw new Error("Authentication failed with Flow2Pay");
          }

          // Extract error message for better error reporting
          const errorMessage = typeof errorData === "object" && errorData.mensagem
            ? errorData.mensagem
            : typeof errorData === "string"
            ? errorData
            : response.statusText;

          throw new Error(`Flow2Pay API error: ${errorMessage}`);
        }

        const data = await response.json();

        // Check for business errors
        if (data.sucesso === false && data.mensagem) {
          logger.warn("Flow2Pay business error", {
            message: data.mensagem,
            data,
            organizationId
          });
          throw new Error(`Flow2Pay business error: ${data.mensagem}`);
        }

        logger.info("Flow2Pay API call successful", { endpoint, organizationId });
        return data as T;

      } finally {
        clearTimeout(timeoutId);
      }

    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));

      // Check if this was a timeout
      const isTimeout = error instanceof Error &&
        (error.name === "AbortError" || error.message.includes("timeout"));

      logger.error("Flow2Pay API error", {
        attempt: attempt + 1,
        max_attempts: retries + 1,
        error: lastError.message,
        is_timeout: isTimeout,
        organizationId
      });

      // Determine if we should retry based on the error
      const shouldRetry = attempt < retries &&
        // Network errors, timeouts, or server errors (5xx)
        (error instanceof TypeError ||
         isTimeout ||
         (error instanceof Error && error.message.includes("500")));

      if (shouldRetry) {
        // Exponential backoff
        const delayMs = calculateBackoffDelay(attempt);
        logger.info("Retrying Flow2Pay API call", {
          delay_ms: delayMs,
          attempt: attempt + 1,
          max_attempts: retries + 1,
          organizationId
        });
        await delay(delayMs);
        continue;
      }

      throw lastError;
    }
  }

  // This should never happen due to the throw in the catch block
  throw lastError || new Error("Unknown error in Flow2Pay API call");
}

/**
 * Create a PIX QR Code payment (charge)
 */
export async function createPixPayment(params: {
  amount: number;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  customerDocument?: string;
  customerDocumentType?: string;
  description?: string;
  postbackUrl?: string;
  organizationId: string;
  externalCode?: string;
  metadata?: Record<string, any>;
  idempotencyKey?: string;
}): Promise<Flow2PayQRCodeProviderResponse> {
  try {
    const {
      amount,
      customerName,
      customerEmail,
      customerDocument,
      description,
      organizationId,
      externalCode,
      idempotencyKey,
      metadata = {}
    } = params;

    logger.info("Creating PIX QR Code payment with Flow2Pay", {
      amount,
      organizationId,
      customerEmail,
      hasDocument: !!customerDocument
    });

    // Validate amount
    if (amount <= 0) {
      throw new Error("Amount must be greater than 0");
    }

    // Generate or use provided transaction ID
    const txId = idempotencyKey || externalCode || generateTxId();

    // Convert amount to centavos
    const amountInCents = toCentavos(amount);

    // Default expiration time: 24 hours
    const expirationTime = 24 * 60 * 60; // 24 hours in seconds

    // Prepare request body
    const requestBody: Flow2PayQRCodeRequest = {
      txId,
      valor: amountInCents,
      tempoExpiracao: expirationTime,
      informacaoAdicional: description || "Pagamento via PIX",
      comImagem: true,
    };

    // Add customer document if provided (for restricted payments)
    if (customerDocument) {
      // Clean document (remove formatting)
      const cleanDocument = customerDocument.replace(/\D/g, "");
      requestBody.numeroDocumento = cleanDocument;

      logger.debug("Including customer document in QR code", {
        organizationId,
        txId,
        hasDocument: true
      });
    }

    // Call Flow2Pay API
    const response = await callFlow2PayAPI<Flow2PayQRCodeResponse>(
      "/qrcode/v2/gerar",
      "POST",
      organizationId,
      requestBody
    );

    logger.info("Flow2Pay QR Code created successfully", {
      txId: response.txid,
      organizationId,
      amount: fromCentavos(response.valor)
    });

    // Extract QR code image
    const qrCodeImage = extractQRCodeImage(response.imagemQrcode);

    // Calculate expiration date
    const expirationDate = new Date(Date.now() + (response.tempoExpiracao || expirationTime) * 1000);

    // Return standardized response
    return {
      success: true,
      externalId: response.txid,
      qrCode: response.qrcode,
      qrCodeImage,
      amount: fromCentavos(response.valor),
      status: "pending",
      expiration: expirationDate.toISOString(),
      txid: response.txid,
      pix: {
        payload: response.qrcode,
        encodedImage: qrCodeImage,
        expirationDate: expirationDate.toISOString(),
        txid: response.txid
      },
      metadata: {
        provider: "FLOW2PAY",
        originalAmount: amount,
        amountInCents,
        expirationTime: response.tempoExpiracao,
        informacaoAdicional: response.informacaoAdicional,
        numeroDocumento: response.numeroDocumento,
        createdAt: new Date().toISOString(),
        ...metadata
      },
      raw: response
    };

  } catch (error) {
    logger.error("Error creating PIX payment with Flow2Pay", {
      error: error instanceof Error ? error.message : String(error),
      organizationId: params.organizationId,
      amount: params.amount
    });
    throw error;
  }
}

/**
 * Process a PIX withdrawal (transfer)
 */
export async function processPixWithdrawal(params: {
  amount: number;
  pixKey: string;
  pixKeyType: string;
  postbackUrl?: string;
  organizationId: string;
  description?: string;
  customerDocument?: string;
  metadata?: Record<string, any>;
}): Promise<Flow2PayTransferProviderResponse> {
  try {
    const {
      amount,
      pixKey,
      pixKeyType,
      organizationId,
      description,
      customerDocument,
      metadata = {}
    } = params;

    logger.info("Processing PIX withdrawal with Flow2Pay", {
      amount,
      pixKeyType,
      organizationId,
      hasCustomerDocument: !!customerDocument
    });

    // Validate amount
    if (amount <= 0) {
      throw new Error("Amount must be greater than 0");
    }

    // Validate PIX key
    const pixKeyTypeEnum = pixKeyType.toUpperCase() as Flow2PayPixKeyType;
    if (!validatePixKey(pixKey, pixKeyTypeEnum)) {
      throw new Error(`Invalid PIX key for type ${pixKeyType}`);
    }

    // Format PIX key for Flow2Pay
    const formattedPixKey = formatPixKey(pixKey, pixKeyTypeEnum);

    // Generate unique send ID (idEnvio) for idempotency
    const idEnvio = generateIdEnvio();

    // Convert amount to centavos
    const amountInCents = toCentavos(amount);

    // Prepare request body
    const requestBody: Flow2PayTransferRequest = {
      idEnvio,
      valor: amountInCents,
      chavePixDestino: formattedPixKey,
      descricao: description || "Transferência via PIX",
    };

    // Add customer document if provided (for restricted transfers)
    if (customerDocument) {
      const cleanDocument = customerDocument.replace(/\D/g, "");
      requestBody.numeroDocumento = cleanDocument;

      logger.debug("Including customer document in transfer", {
        organizationId,
        idEnvio,
        hasDocument: true
      });
    }

    // Call Flow2Pay API
    const response = await callFlow2PayAPI<Flow2PayTransferResponse>(
      "/pix/v1/transferir",
      "POST",
      organizationId,
      requestBody
    );

    logger.info("Flow2Pay PIX transfer initiated successfully", {
      idEnvio: response.idEnvio,
      status: response.status,
      organizationId,
      amount
    });

    // Return standardized response
    return {
      success: true,
      externalId: response.idEnvio,
      idEnvio: response.idEnvio,
      status: mapFlow2PayStatus(response.status),
      amount,
      message: response.message,
      metadata: {
        provider: "FLOW2PAY",
        originalAmount: amount,
        amountInCents,
        pixKey: formattedPixKey,
        pixKeyType,
        description: requestBody.descricao,
        numeroDocumento: requestBody.numeroDocumento,
        createdAt: new Date().toISOString(),
        ...metadata
      },
      raw: response
    };

  } catch (error) {
    logger.error("Error processing PIX withdrawal with Flow2Pay", {
      error: error instanceof Error ? error.message : String(error),
      organizationId: params.organizationId,
      amount: params.amount,
      pixKeyType: params.pixKeyType
    });
    throw error;
  }
}

/**
 * Get transaction status
 */
export async function getTransactionStatus(params: {
  transactionId: string;
  organizationId: string;
  transactionType?: 'CHARGE' | 'SEND';
}): Promise<Flow2PayProviderResponse> {
  try {
    const { transactionId, organizationId, transactionType } = params;

    logger.info("Getting transaction status from Flow2Pay", {
      transactionId,
      transactionType,
      organizationId
    });

    // Prepare query parameters - try different approaches based on transaction type
    let queryParams: Flow2PayTransactionQuery;

    if (transactionType === 'SEND') {
      // For transfers, use idEnvio
      queryParams = { idEnvio: transactionId };
    } else if (transactionType === 'CHARGE') {
      // For QR codes, use codigoTransacao (txid)
      queryParams = { codigoTransacao: transactionId };
    } else {
      // If type is unknown, try both approaches
      queryParams = { codigoTransacao: transactionId };
    }

    // Build query string
    const queryString = Object.entries(queryParams)
      .filter(([_, value]) => value !== undefined)
      .map(([key, value]) => `${key}=${encodeURIComponent(value!)}`)
      .join('&');

    // Call Flow2Pay API
    const response = await callFlow2PayAPI<Flow2PayTransactionResponse>(
      `/transacao/v1/buscar?${queryString}`,
      "GET",
      organizationId
    );

    logger.info("Flow2Pay transaction status retrieved", {
      transactionId,
      status: response.status,
      organizationId
    });

    // Return standardized response
    return {
      success: true,
      transactionId: response.codigoTransacao,
      externalId: response.idEnvio || response.codigoTransacao,
      status: mapFlow2PayStatus(response.status),
      amount: response.valor ? fromCentavos(response.valor) : undefined,
      metadata: {
        provider: "FLOW2PAY",
        endToEndId: response.endToEndId,
        chavePix: response.chavePix,
        horario: response.horario,
        recebedor: response.recebedor,
        erro: response.erro,
        retrievedAt: new Date().toISOString()
      },
      raw: response
    };

  } catch (error) {
    // If the first attempt fails and we don't know the transaction type, try the other approach
    if (!params.transactionType) {
      try {
        logger.info("Retrying transaction status with idEnvio", {
          transactionId: params.transactionId,
          organizationId: params.organizationId
        });

        const queryString = `idEnvio=${encodeURIComponent(params.transactionId)}`;
        const response = await callFlow2PayAPI<Flow2PayTransactionResponse>(
          `/transacao/v1/buscar?${queryString}`,
          "GET",
          params.organizationId
        );

        return {
          success: true,
          transactionId: response.codigoTransacao,
          externalId: response.idEnvio || response.codigoTransacao,
          status: mapFlow2PayStatus(response.status),
          amount: response.valor ? fromCentavos(response.valor) : undefined,
          metadata: {
            provider: "FLOW2PAY",
            endToEndId: response.endToEndId,
            chavePix: response.chavePix,
            horario: response.horario,
            recebedor: response.recebedor,
            erro: response.erro,
            retrievedAt: new Date().toISOString()
          },
          raw: response
        };
      } catch (retryError) {
        logger.warn("Both transaction status approaches failed", {
          originalError: error instanceof Error ? error.message : String(error),
          retryError: retryError instanceof Error ? retryError.message : String(retryError),
          transactionId: params.transactionId,
          organizationId: params.organizationId
        });
      }
    }

    logger.error("Error getting transaction status from Flow2Pay", {
      error: error instanceof Error ? error.message : String(error),
      transactionId: params.transactionId,
      organizationId: params.organizationId,
      transactionType: params.transactionType
    });
    throw error;
  }
}

/**
 * Process a PIX refund (reversal)
 */
export async function processRefund(params: {
  transactionId: string;
  amount: number;
  reason?: string;
  organizationId: string;
  endToEndId?: string;
  metadata?: Record<string, any>;
}): Promise<Flow2PayProviderResponse> {
  try {
    const {
      transactionId,
      amount,
      reason,
      organizationId,
      endToEndId,
      metadata = {}
    } = params;

    logger.info("Processing PIX refund with Flow2Pay", {
      transactionId,
      amount,
      organizationId,
      hasEndToEndId: !!endToEndId
    });

    // Validate amount
    if (amount <= 0) {
      throw new Error("Refund amount must be greater than 0");
    }

    // If endToEndId is not provided, try to get it from transaction status
    let finalEndToEndId = endToEndId;
    if (!finalEndToEndId) {
      logger.info("EndToEndId not provided, fetching from transaction status", {
        transactionId,
        organizationId
      });

      try {
        const statusResponse = await getTransactionStatus({
          transactionId,
          organizationId,
          transactionType: 'CHARGE' // Refunds are typically for received payments
        });

        finalEndToEndId = statusResponse.metadata?.endToEndId;

        if (!finalEndToEndId) {
          throw new Error("Could not retrieve endToEndId for refund");
        }

        logger.info("Retrieved endToEndId from transaction status", {
          transactionId,
          endToEndId: finalEndToEndId,
          organizationId
        });
      } catch (statusError) {
        logger.error("Failed to retrieve endToEndId for refund", {
          error: statusError instanceof Error ? statusError.message : String(statusError),
          transactionId,
          organizationId
        });
        throw new Error("EndToEndId is required for refunds and could not be retrieved from transaction");
      }
    }

    // Convert amount to centavos
    const amountInCents = toCentavos(amount);

    // Prepare request body
    const requestBody: Flow2PayRefundRequest = {
      endToEnd: finalEndToEndId,
      valor: amountInCents,
      descricao: reason || "Estorno de pagamento PIX"
    };

    // Call Flow2Pay API
    const response = await callFlow2PayAPI<Flow2PayRefundResponse>(
      "/estorno/v1/pix-in",
      "POST",
      organizationId,
      requestBody
    );

    logger.info("Flow2Pay PIX refund processed successfully", {
      endToEndId: response.endToEndId,
      status: response.status,
      organizationId,
      amount
    });

    // Return standardized response
    return {
      success: true,
      transactionId,
      externalId: response.endToEndId,
      status: mapFlow2PayStatus(response.status),
      amount: fromCentavos(response.valor),
      message: response.descricao,
      metadata: {
        provider: "FLOW2PAY",
        originalAmount: amount,
        amountInCents,
        endToEndId: response.endToEndId,
        horario: response.horario,
        reason: requestBody.descricao,
        processedAt: new Date().toISOString(),
        ...metadata
      },
      raw: response
    };

  } catch (error) {
    logger.error("Error processing PIX refund with Flow2Pay", {
      error: error instanceof Error ? error.message : String(error),
      transactionId: params.transactionId,
      organizationId: params.organizationId,
      amount: params.amount
    });
    throw error;
  }
}
