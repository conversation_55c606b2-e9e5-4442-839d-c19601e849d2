import crypto from "crypto";
import { logger } from "@repo/logs";
import type { Flow2PayPixKeyType, Flow2PayTransactionStatus } from "./types";

/**
 * Convert amount to centavos (cents) for Flow2Pay API
 * @param amount Amount in BRL (e.g., 10.50)
 * @returns Amount in centavos (e.g., 1050)
 */
export function toCentavos(amount: number): number {
  return Math.round(amount * 100);
}

/**
 * Convert centavos (cents) to BRL amount
 * @param centavos Amount in centavos (e.g., 1050)
 * @returns Amount in BRL (e.g., 10.50)
 */
export function fromCentavos(centavos: number): number {
  return centavos / 100;
}

/**
 * Validates that a transaction ID meets Flow2Pay's requirements
 * @param txid Transaction ID to validate
 * @returns True if valid, false otherwise
 */
export function isValidTxId(txid: string): boolean {
  // Flow2Pay requires txids to be 26-35 alphanumeric characters
  const validRegex = /^[a-zA-Z0-9]{26,35}$/;
  const isValid = validRegex.test(txid);

  if (!isValid) {
    const length = txid ? txid.length : 0;
    const hasInvalidChars = txid ? /[^a-zA-Z0-9]/.test(txid) : false;

    logger.warn("Invalid txId format", {
      txid,
      length,
      required_length: "26-35",
      required_format: "alphanumeric [a-zA-Z0-9]",
      length_issue: length < 26 ? "too_short" : length > 35 ? "too_long" : null,
      has_invalid_chars: hasInvalidChars,
    });
  }

  return isValid;
}

/**
 * Generate a unique transaction ID that complies with Flow2Pay requirements
 * Transaction IDs must be between 26-35 alphanumeric characters
 */
export function generateTxId(): string {
  // Generate a random string of 16 characters
  const randomBytes = crypto.randomBytes(16).toString("hex");

  // Add a timestamp prefix to ensure uniqueness
  const timestamp = Date.now().toString(36);

  // Combine and ensure it's lowercase and within the required length
  let txid = (timestamp + randomBytes).toLowerCase();

  // Trim to maximum length if needed
  if (txid.length > 35) {
    txid = txid.substring(0, 35);
  }

  // Ensure minimum length by padding if needed
  if (txid.length < 26) {
    const padding = "abcdefghijklmnopqrstuvwxyz0123456789";
    while (txid.length < 26) {
      txid += padding.charAt(Math.floor(Math.random() * padding.length));
    }
  }

  // Validate the generated txid
  if (!isValidTxId(txid)) {
    logger.error("Generated invalid txid", { txid });
    // Fallback to a guaranteed valid format
    return "a".repeat(30);
  }

  logger.debug("Generated txid", { txid, length: txid.length });
  return txid;
}

/**
 * Generate a unique idEnvio (send ID) for PIX transfers
 * Must be 1-36 characters for Flow2Pay API
 */
export function generateIdEnvio(): string {
  const timestamp = Date.now().toString(36);
  const random = crypto.randomBytes(8).toString("hex");
  const idEnvio = `${timestamp}${random}`.substring(0, 36);
  
  logger.debug("Generated idEnvio", { idEnvio, length: idEnvio.length });
  return idEnvio;
}

/**
 * Clean and validate CPF/CNPJ document number
 * @param document Document number with or without formatting
 * @returns Clean document number (only digits)
 */
export function cleanDocument(document: string): string {
  return document.replace(/\D/g, "");
}

/**
 * Validate CPF format
 * @param cpf CPF number (with or without formatting)
 * @returns True if valid CPF format
 */
export function isValidCPF(cpf: string): boolean {
  const cleanCpf = cleanDocument(cpf);
  return cleanCpf.length === 11 && /^\d{11}$/.test(cleanCpf);
}

/**
 * Validate CNPJ format
 * @param cnpj CNPJ number (with or without formatting)
 * @returns True if valid CNPJ format
 */
export function isValidCNPJ(cnpj: string): boolean {
  const cleanCnpj = cleanDocument(cnpj);
  return cleanCnpj.length === 14 && /^\d{14}$/.test(cleanCnpj);
}

/**
 * Validate email format
 * @param email Email address
 * @returns True if valid email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.toLowerCase());
}

/**
 * Validate phone number format (Brazilian)
 * @param phone Phone number
 * @returns True if valid phone format
 */
export function isValidPhone(phone: string): boolean {
  const cleanPhone = cleanDocument(phone);
  // Brazilian phone: 10 or 11 digits
  return cleanPhone.length >= 10 && cleanPhone.length <= 11 && /^\d+$/.test(cleanPhone);
}

/**
 * Validate PIX key based on its type
 * @param pixKey PIX key value
 * @param pixKeyType PIX key type
 * @returns True if valid for the specified type
 */
export function validatePixKey(pixKey: string, pixKeyType: Flow2PayPixKeyType): boolean {
  switch (pixKeyType.toUpperCase()) {
    case "CPF":
      return isValidCPF(pixKey);
    case "CNPJ":
      return isValidCNPJ(pixKey);
    case "EMAIL":
      return isValidEmail(pixKey);
    case "PHONE":
      return isValidPhone(pixKey);
    case "RANDOM":
      // Random keys are UUIDs or similar, just check basic format
      return pixKey.length >= 32 && /^[a-zA-Z0-9-]+$/.test(pixKey);
    default:
      return false;
  }
}

/**
 * Format PIX key for Flow2Pay API requirements
 * @param pixKey PIX key value
 * @param pixKeyType PIX key type
 * @returns Formatted PIX key
 */
export function formatPixKey(pixKey: string, pixKeyType: Flow2PayPixKeyType): string {
  switch (pixKeyType.toUpperCase()) {
    case "CPF":
    case "CNPJ":
    case "PHONE":
      return cleanDocument(pixKey);
    case "EMAIL":
      return pixKey.toLowerCase().trim();
    case "RANDOM":
      return pixKey.trim();
    default:
      return pixKey;
  }
}

/**
 * Map Flow2Pay status to standardized transaction status
 * @param flow2payStatus Status from Flow2Pay API
 * @returns Standardized transaction status
 */
export function mapFlow2PayStatus(flow2payStatus: string): Flow2PayTransactionStatus {
  const status = flow2payStatus.toLowerCase();
  
  switch (status) {
    case "sucesso":
    case "success":
    case "completed":
      return "completed";
    case "falha":
    case "erro":
    case "failed":
    case "error":
      return "failed";
    case "em processamento":
    case "processing":
    case "pendente":
    case "pending":
      return "processing";
    case "estornado":
    case "reversed":
      return "reversed";
    default:
      logger.warn("Unknown Flow2Pay status", { flow2payStatus });
      return "pending";
  }
}

/**
 * Extract QR code image from Flow2Pay response
 * Flow2Pay may return the image in different formats
 * @param imagemQrcode Image data from Flow2Pay response
 * @returns Base64 encoded image string or empty string
 */
export function extractQRCodeImage(imagemQrcode: any): string {
  if (!imagemQrcode) {
    return "";
  }

  // If it's already a string, return it
  if (typeof imagemQrcode === "string") {
    return imagemQrcode;
  }

  // If it's an object, try to extract the image data
  if (typeof imagemQrcode === "object") {
    // Try different property names that Flow2Pay might use
    const possibleProps = ["data", "base64", "image", "content"];
    
    for (const prop of possibleProps) {
      if (imagemQrcode[prop] && typeof imagemQrcode[prop] === "string") {
        return imagemQrcode[prop];
      }
    }

    // If no string property found, try to stringify the object
    try {
      return JSON.stringify(imagemQrcode);
    } catch (e) {
      logger.warn("Failed to stringify QR code image object", { error: e });
      return "";
    }
  }

  // For any other type, try to convert to string
  try {
    return String(imagemQrcode);
  } catch (e) {
    logger.warn("Failed to convert QR code image to string", { error: e });
    return "";
  }
}

/**
 * Create a delay promise for retry logic
 * @param ms Milliseconds to delay
 * @returns Promise that resolves after the delay
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Calculate exponential backoff delay
 * @param attempt Current attempt number (0-based)
 * @param baseDelay Base delay in milliseconds
 * @param maxDelay Maximum delay in milliseconds
 * @returns Calculated delay in milliseconds
 */
export function calculateBackoffDelay(attempt: number, baseDelay: number = 100, maxDelay: number = 2000): number {
  return Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
}
