import { logger } from "@repo/logs";
import * as reflowpay from "./reflowpay";
import * as primepag from "./primepag";

import * as pixium from "./pixium";
import * as transfeera from "./transfeera";
import * as mediuspag from "./mediuspag";
import * as mocksim from "./mocksim";
import * as pluggouPix from "./pluggou-pix";
import * as flow2pay from "./flow2pay";
import { db } from "@repo/database";

// Cache for gateway credentials to reduce database queries
interface CacheEntry {
  credentials: Record<string, any>;
  timestamp: number;
}

const credentialsCache = new Map<string, CacheEntry>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds

// Helper to get credentials from environment variables
function getEnvironmentCredentials(gatewayType: string): Record<string, any> | null {
  const prefix = `${gatewayType}_`;

  switch (gatewayType) {
    case 'PLUGGOU_PIX':
      return {
        apiKey: process.env.PLUGGOU_PIX_API_KEY,
        apiUrl: process.env.PLUGGOU_PIX_API_URL,
        environment: process.env.PLUGGOU_PIX_ENVIRONMENT || 'sandbox',
        webhookSecret: process.env.PLUGGOU_PIX_WEBHOOK_SECRET,
      };

    case 'FLOW2PAY':
      return {
        clientId: process.env.FLOW2PAY_CLIENT_ID,
        clientSecret: process.env.FLOW2PAY_CLIENT_SECRET,
        eventToken: process.env.FLOW2PAY_EVENT_TOKEN,
        apiUrl: process.env.FLOW2PAY_API_URL,
        environment: process.env.FLOW2PAY_ENVIRONMENT || 'sandbox',
        timeout: process.env.FLOW2PAY_TIMEOUT ? parseInt(process.env.FLOW2PAY_TIMEOUT) : undefined,
        retries: process.env.FLOW2PAY_RETRIES ? parseInt(process.env.FLOW2PAY_RETRIES) : undefined,
      };

    case 'TRANSFEERA':
      return {
        clientId: process.env.TRANSFEERA_CLIENT_ID,
        clientSecret: process.env.TRANSFEERA_CLIENT_SECRET,
        environment: process.env.TRANSFEERA_ENVIRONMENT || 'sandbox',
        webhookSecret: process.env.TRANSFEERA_WEBHOOK_SECRET,
      };

    case 'PIXIUM':
      return {
        apiKey: process.env.PIXIUM_API_KEY,
        apiSecret: process.env.PIXIUM_API_SECRET,
        environment: process.env.PIXIUM_ENVIRONMENT || 'sandbox',
        webhookSecret: process.env.PIXIUM_WEBHOOK_SECRET,
      };

    case 'REFLOWPAY':
      return {
        apiKey: process.env.REFLOWPAY_API_KEY,
        apiSecret: process.env.REFLOWPAY_API_SECRET,
        environment: process.env.REFLOWPAY_ENVIRONMENT || 'sandbox',
        webhookSecret: process.env.REFLOWPAY_WEBHOOK_SECRET,
      };

    case 'PRIMEPAG':
      return {
        apiKey: process.env.PRIMEPAG_API_KEY,
        apiSecret: process.env.PRIMEPAG_API_SECRET,
        environment: process.env.PRIMEPAG_ENVIRONMENT || 'sandbox',
        webhookSecret: process.env.PRIMEPAG_WEBHOOK_SECRET,
      };

    case 'MEDIUSPAG':
      return {
        apiKey: process.env.MEDIUSPAG_API_KEY,
        apiSecret: process.env.MEDIUSPAG_API_SECRET,
        environment: process.env.MEDIUSPAG_ENVIRONMENT || 'sandbox',
        webhookSecret: process.env.MEDIUSPAG_WEBHOOK_SECRET,
      };

    case 'MOCKSIM':
      return {
        apiKey: process.env.MOCKSIM_API_KEY || 'mock_key',
        apiSecret: process.env.MOCKSIM_API_SECRET || 'mock_secret',
        environment: 'sandbox',
      };

    default:
      logger.warn(`No environment credential mapping for gateway type: ${gatewayType}`);
      return null;
  }
}

// Common interface for payment gateway operations
export interface PaymentGatewayProvider {
  createPixPayment(params: {
    amount: number;
    customerName: string;
    customerEmail: string;
    customerPhone?: string;
    customerDocument?: string;
    customerDocumentType?: string;
    description?: string;
    postbackUrl?: string;
    organizationId: string;
    externalCode?: string;
    metadata?: Record<string, any>;
    idempotencyKey?: string;
  }): Promise<any>;

  processPixWithdrawal(params: {
    amount: number;
    pixKey: string;
    pixKeyType: string;
    postbackUrl?: string;
    organizationId: string;
  }): Promise<any>;

  getTransactionStatus(params: {
    transactionId: string;
    organizationId: string;
    transactionType?: 'CHARGE' | 'SEND';
  }): Promise<any>;

  processRefund(params: {
    transactionId: string;
    amount: number;
    reason?: string;
    organizationId: string;
  }): Promise<any>;
}

// Factory to get the appropriate provider based on gateway type
export async function getPaymentProvider(organizationId: string, options?: {
  forceType?: string;
  preferredType?: string;
  action?: 'charge' | 'withdrawal' | 'refund' | 'status';
}) {
  try {
    // PRIORITY: Default to PLUGGOU_PIX for maximum speed and performance
    // Use environment variable for fast fallback without database queries
    const defaultGatewayType = process.env.DEFAULT_PAYMENT_GATEWAY?.toUpperCase() || 'PLUGGOU_PIX';

    // Special handling for MockSim - forçar uso do gateway mock quando solicitado
    if (options?.forceType?.toUpperCase() === 'MOCKSIM') {
      logger.info(`Using MockSim payment gateway for testing`, {
        organizationId,
        action: options?.action
      });
      return mocksim;
    }

    if (!options?.forceType && !options?.preferredType) {
      logger.info(`🚀 Using ${defaultGatewayType} as PRIMARY gateway for maximum transaction speed`, {
        organizationId,
        action: options?.action,
        source: process.env.DEFAULT_PAYMENT_GATEWAY ? 'environment' : 'fallback',
        performanceOptimized: true
      });

      // Return provider directly without database queries for speed
      return getProviderByType(defaultGatewayType);
    }

    // Transfeera agora pode ser usado para todas as operações (charge, withdrawal, status, refund)
    // Removemos a restrição anterior que limitava o Transfeera apenas para transferências

    let gateway;
    const actionRequiresReceive = options?.action === 'charge' || options?.action === 'refund';
    const actionRequiresSend = options?.action === 'withdrawal';

    // Base query
    const baseQuery = {
      isActive: true,
      ...(actionRequiresReceive && { canReceive: true }),
      ...(actionRequiresSend && { canSend: true }),
    };

    if (options?.forceType) {
      // Use specific gateway type if requested
      gateway = await db.paymentGateway.findFirst({
        where: {
          ...baseQuery,
          type: options.forceType,
        },
        orderBy: [
          { priority: 'asc' },
          { createdAt: 'desc' },
        ],
      });
    } else if (options?.preferredType) {
      // Use the preferred gateway type directly for speed
      logger.info(`Using preferred gateway type: ${options.preferredType}`, {
        organizationId,
        action: options?.action,
        preferredType: options.preferredType
      });

      gateway = await db.paymentGateway.findFirst({
        where: {
          ...baseQuery,
          type: options.preferredType,
        },
        orderBy: [
          { priority: 'asc' },
          { createdAt: 'desc' },
        ],
      });
    }

    // If no specific gateway found, try to find one for this organization
    if (!gateway) {
      // PRIORITY: First try to find PLUGGOU_PIX for this organization
      logger.info(`Looking for ${defaultGatewayType} gateway for organization`, { organizationId });

      gateway = await db.paymentGateway.findFirst({
        where: {
          ...baseQuery,
          type: defaultGatewayType,
          organizations: {
            some: {
              organizationId,
              isActive: true
            }
          }
        },
        orderBy: [
          { priority: 'asc' },
          { createdAt: 'desc' },
        ],
      });

      if (gateway) {
        logger.info(`🎯 Found ${defaultGatewayType} gateway for organization`, {
          gatewayId: gateway.id,
          organizationId,
          action: options?.action
        });
      } else {
        // Fallback: Look for organization-specific default gateway
        logger.info(`${defaultGatewayType} not found, looking for organization-specific default gateway`, { organizationId });

        gateway = await db.paymentGateway.findFirst({
          where: {
            ...baseQuery,
            organizations: {
              some: {
                organizationId,
                isActive: true,
                isDefault: true
              }
            }
          },
          orderBy: [
            { priority: 'asc' },
            { createdAt: 'desc' },
          ],
        });
      }
    }

    // If no organization-specific default gateway found, try any gateway for this organization
    if (!gateway) {
      logger.info("Looking for any organization-specific gateway", { organizationId });

      gateway = await db.paymentGateway.findFirst({
        where: {
          ...baseQuery,
          organizations: {
            some: {
              organizationId,
              isActive: true
            }
          }
        },
        orderBy: [
          { priority: 'asc' },
          { createdAt: 'desc' },
        ],
      });
    }

    // If no organization-specific gateway found, try a global default gateway
    if (!gateway) {
      // PRIORITY: First try to find global PLUGGOU_PIX gateway
      logger.info(`Looking for global ${defaultGatewayType} gateway`, { organizationId });

      gateway = await db.paymentGateway.findFirst({
        where: {
          ...baseQuery,
          type: defaultGatewayType,
          isGlobal: true
        },
        orderBy: [
          { priority: 'asc' },
          { createdAt: 'desc' },
        ],
      });

      if (gateway) {
        logger.info(`🌍 Found global ${defaultGatewayType} gateway`, {
          gatewayId: gateway.id,
          organizationId,
          action: options?.action
        });
      } else {
        // Fallback: Look for any global default gateway
        logger.info(`Global ${defaultGatewayType} not found, looking for global default gateway`, { organizationId });

        gateway = await db.paymentGateway.findFirst({
          where: {
            ...baseQuery,
            isDefault: true,
            isGlobal: true
          },
          orderBy: [
            { priority: 'asc' },
            { createdAt: 'desc' },
          ],
        });
      }
    }

    // If still no gateway found, try any active gateway that matches the capability
    if (!gateway) {
      logger.info("Looking for any active gateway", { organizationId });

      gateway = await db.paymentGateway.findFirst({
        where: baseQuery,
        orderBy: [
          { priority: 'asc' },
          { createdAt: 'desc' },
        ],
      });
    }

    if (!gateway) {
      if (actionRequiresReceive) {
        throw new Error(`No active payment gateway configured for receiving payments`);
      } else if (actionRequiresSend) {
        throw new Error(`No active payment gateway configured for sending payments`);
      } else {
        throw new Error(`No active payment gateway found in the system`);
      }
    }

    // Enhanced logging with special indication for PLUGGOU_PIX
    const isPrimaryGateway = gateway.type === defaultGatewayType;
    const logMessage = isPrimaryGateway
      ? `🚀 Using ${defaultGatewayType} as PRIMARY gateway (optimized for maximum transaction speed)`
      : `Using payment gateway: ${gateway.type}`;

    logger.info(logMessage, {
      gatewayId: gateway.id,
      gatewayType: gateway.type,
      isPrimaryGateway,
      defaultGatewayType,
      priority: gateway.priority,
      action: options?.action,
      canReceive: gateway.canReceive,
      canSend: gateway.canSend,
      forceType: options?.forceType,
      preferredType: options?.preferredType,
      isDefault: gateway.isDefault,
      isGlobal: gateway.isGlobal,
      organizationId,
      performanceOptimized: isPrimaryGateway,
      envSource: process.env.DEFAULT_PAYMENT_GATEWAY ? 'environment' : 'fallback'
    });

    // Initialize the provider based on the gateway type
    return getProviderByType(gateway.type);
  } catch (error) {
    logger.error("Error getting payment provider", { error, organizationId, options });
    throw error;
  }
}

// Get provider by gateway type
export function getProviderByType(type: string): PaymentGatewayProvider {
  switch (type.toUpperCase()) {
    case "REFLOWPAY":
      return reflowpay;
    case "PRIMEPAG":
      return primepag;

    case "PIXIUM":
      return pixium;
    case "TRANSFEERA":
      // Transfeera pode ser usado para transferências e recebimentos
      return transfeera;

    case "PLUGGOU_PIX":
      // Pluggou PIX API (integração com Flow2Pay)
      return pluggouPix;

    case "FLOW2PAY":
      // Direct Flow2Pay integration (eliminates intermediate layer)
      return flow2pay;

    case "MEDIUSPAG":
      return mediuspag;
    case "MOCKSIM":
      // Gateway de simulação para testes
      return mocksim;
    // Add more gateway implementations as they become available
    default:
      throw new Error(`Unsupported payment gateway type: ${type}`);
  }
}

// Helper to get credentials for a gateway
export async function getGatewayCredentials(
  organizationId: string,
  type: string
): Promise<Record<string, any>> {
  const cacheKey = `${organizationId}:${type.toUpperCase()}`;
  const defaultGatewayType = process.env.DEFAULT_PAYMENT_GATEWAY?.toUpperCase() || 'PLUGGOU_PIX';

  // Check cache first
  const cached = credentialsCache.get(cacheKey);
  if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
    logger.info(`Using cached credentials for gateway type ${type} for organization ${organizationId}`);
    return cached.credentials;
  }

  // 🚀 PRIORITY: If this is the default gateway type defined by ENV, use environment credentials
  if (type.toUpperCase() === defaultGatewayType && process.env.DEFAULT_PAYMENT_GATEWAY) {
    logger.info(`🚀 Using environment credentials for default gateway ${type}`, {
      organizationId,
      gatewayType: type,
      source: 'environment'
    });

    const envCredentials = getEnvironmentCredentials(type.toUpperCase());
    if (envCredentials && Object.keys(envCredentials).length > 0) {
      logger.info(`✅ Environment credentials found for ${type}`, {
        keys: Object.keys(envCredentials),
        organizationId
      });

      // Cache the environment credentials
      credentialsCache.set(cacheKey, {
        credentials: envCredentials,
        timestamp: Date.now()
      });

      return envCredentials;
    } else {
      logger.warn(`⚠️ No environment credentials found for default gateway ${type}, falling back to database`, {
        organizationId
      });
    }
  }

  logger.info(`Getting credentials for gateway type ${type} for organization ${organizationId}`);

  try {
    // First, try to find the organization-gateway relationship
    const orgGateway = await db.organizationGateway.findFirst({
      where: {
        organizationId: organizationId,
        isActive: true,
        gateway: {
          type: type.toUpperCase(),
          isActive: true
        }
      },
      include: {
        gateway: true
      },
      orderBy: [
        { priority: 'asc' },
        { createdAt: 'desc' },
      ],
    });

    logger.info(`Organization-gateway query result:`, {
      found: !!orgGateway,
      gatewayId: orgGateway?.gateway?.id,
      hasRelationshipCredentials: (orgGateway as any)?.credentials ? true : false,
      hasGatewayCredentials: orgGateway?.gateway?.credentials ? true : false
    });

    // If we found an organization-gateway relationship
    if (orgGateway) {
      logger.info(`Found organization-specific ${type} gateway: ${orgGateway.gateway.id}`);

      // Check if the relationship has its own credentials
      if ((orgGateway as any).credentials && Object.keys((orgGateway as any).credentials as Record<string, any>).length > 0) {
        logger.info(`Using credentials from organization-gateway relationship`);
        const creds = (orgGateway as any).credentials as Record<string, any>;
        logger.info(`Credential keys available:`, { keys: Object.keys(creds) });

        // Cache the credentials
        credentialsCache.set(cacheKey, {
          credentials: creds,
          timestamp: Date.now()
        });

        return creds;
      }

      // If no credentials in the relationship, use the gateway's credentials
      if (orgGateway.gateway.credentials && Object.keys(orgGateway.gateway.credentials as Record<string, any>).length > 0) {
        logger.info(`Using credentials from gateway`);
        const creds = orgGateway.gateway.credentials as Record<string, any>;
        logger.info(`Credential keys available:`, { keys: Object.keys(creds) });

        // Cache the credentials
        credentialsCache.set(cacheKey, {
          credentials: creds,
          timestamp: Date.now()
        });

        return creds;
      }

      logger.error(`Found gateway relationship but no credentials available`);
    }

    // If no organization-specific gateway found, try to find a global one
    logger.info(`No organization-specific ${type} gateway found or no credentials, looking for global gateway`);

    // Check if there are any gateways of this type at all
    const allGateways = await db.paymentGateway.findMany({
      where: {
        type: type.toUpperCase(),
      },
      select: {
        id: true,
        isActive: true,
        isGlobal: true,
        credentials: true
      }
    });

    logger.info(`All gateways of type ${type}:`, {
      count: allGateways.length,
      gateways: allGateways.map(g => ({
        id: g.id,
        isActive: g.isActive,
        isGlobal: g.isGlobal,
        hasCredentials: g.credentials ? true : false
      }))
    });

    const gateway = await db.paymentGateway.findFirst({
      where: {
        type: type.toUpperCase(),
        isActive: true,
        isGlobal: true
      },
      orderBy: [
        { priority: 'asc' },
        { createdAt: 'desc' },
      ],
    });

    if (!gateway) {
      logger.error(`No active ${type} gateway found in the system for organization ${organizationId}`);
      throw new Error(`No active ${type} gateway found in the system`);
    }

    logger.info(`Found global ${type} gateway: ${gateway.id}`);

    if (!gateway.credentials || Object.keys(gateway.credentials as Record<string, any>).length === 0) {
      logger.error(`Gateway ${gateway.id} has no credentials`);
      throw new Error(`Gateway ${gateway.id} has no credentials`);
    }

    const creds = gateway.credentials as Record<string, any>;
    logger.info(`Global gateway credential keys available:`, { keys: Object.keys(creds) });

    // Cache the credentials
    credentialsCache.set(cacheKey, {
      credentials: creds,
      timestamp: Date.now()
    });

    return creds;
  } catch (error) {
    logger.error(`Error in getGatewayCredentials for ${type}`, { error, organizationId });
    throw error;
  }
}

// Function to process a transaction with the appropriate gateway
export async function processTransaction(params: {
  transactionId: string;
  organizationId: string;
  gatewayType?: string;
  action: 'status' | 'refund' | 'withdrawal';
  additionalParams?: Record<string, any>;
}): Promise<any> {
  const { transactionId, organizationId, gatewayType, action, additionalParams = {} } = params;

  try {
    const provider = await getPaymentProvider(organizationId, {
      preferredType: gatewayType,
      action: action
    });

    // Transfeera agora pode ser usado para todas as operações
    // Removemos a restrição anterior

    switch (action) {
      case 'status':
        return provider.getTransactionStatus({
          transactionId,
          organizationId,
          transactionType: additionalParams.transactionType
        });
      case 'refund':
        return provider.processRefund({
          transactionId,
          organizationId,
          amount: additionalParams.amount,
          reason: additionalParams.reason
        });
      case 'withdrawal':
        return provider.processPixWithdrawal({
          amount: additionalParams.amount,
          pixKey: additionalParams.pixKey,
          pixKeyType: additionalParams.pixKeyType,
          postbackUrl: additionalParams.postbackUrl,
          organizationId,
        });
      default:
        throw new Error(`Unsupported action: ${action}`);
    }
  } catch (error) {
    logger.error(`Error processing ${action}`, { error, transactionId, organizationId });
    throw error;
  }
}
