
export * from "./factory";
export * as reflowpay from "./reflowpay";
export * as primepag from "./primepag";
export * as asaas from "./asaas";
export * as pixium from "./pixium";
export * as transfeera from "./transfeera";
export * as mercadopago from "./mercadopago";
export * as flow2pay from "./flow2pay";

export interface ProcessCheckoutParams {
  productId: string;
  amount: number;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  customerDocument?: string;
  description?: string;
  organizationId: string;
  metadata?: Record<string, any>;
}

export interface CheckoutResponse {
  success: boolean;
  transactionId: string;
  paymentMethod: 'PIX' | 'CREDIT_CARD' | 'BOLETO';
  orderId: string;
  pixCode?: string;
  pixQrCode?: string;
  pixExpiresAt?: Date;
}
