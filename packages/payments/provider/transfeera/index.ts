import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { getGatewayCredentials } from "../factory";

// Base URL for Transfeera API
const TRANSFEERA_API_BASE_URL = {
  production: "https://api.transfeera.com",
  sandbox: "https://api.sandbox.transfeera.com",
};

// Authentication URL for Transfeera
const TRANSFEERA_AUTH_URL = {
  production: "https://login-api.transfeera.com/authorization",
  sandbox: "https://login-api.sandbox.transfeera.com/authorization",
};

// Helper to get the Transfeera credentials for an organization
export async function getTransfeeraCredentials(organizationId: string): Promise<{
  clientId: string;
  clientSecret: string;
  environment: "production" | "sandbox";
}> {
  try {
    const credentials = await getGatewayCredentials(organizationId, "TRANSFEERA");
    return {
      clientId: credentials.clientId as string,
      clientSecret: credentials.clientSecret as string,
      environment: (credentials.environment as "production" | "sandbox") || "sandbox",
    };
  } catch (error) {
    logger.error("Error getting Transfeera credentials", { error, organizationId });
    throw new Error(`Failed to get Transfeera credentials for organization ${organizationId}`);
  }
}

// Helper to get an access token for Transfeera API
export async function getTransfeeraAccessToken(organizationId: string): Promise<string> {
  try {
    const { clientId, clientSecret, environment } = await getTransfeeraCredentials(organizationId);
    const authUrl = TRANSFEERA_AUTH_URL[environment];

    logger.info("Requesting Transfeera access token", {
      environment,
      authUrl,
      organizationId
    });

    const response = await fetch(authUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "User-Agent": "OrionPay (<EMAIL>)"
      },
      body: JSON.stringify({
        grant_type: "client_credentials",
        client_id: clientId,
        client_secret: clientSecret,
      }),
    });

    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = await response.text();
      }

      logger.error("Error getting Transfeera access token", {
        error: errorData,
        status: response.status,
        organizationId
      });
      throw new Error(`Failed to get Transfeera access token: ${JSON.stringify(errorData)}`);
    }

    const data = await response.json();
    logger.info("Successfully obtained Transfeera access token", {
      expiresIn: data.expires_in,
      organizationId
    });

    return data.access_token;
  } catch (error) {
    logger.error("Error in getTransfeeraAccessToken", { error, organizationId });
    throw error;
  }
}

// Create a Pix payment (charge)
export async function createPixPayment(params: {
  amount: number;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  customerDocument?: string;
  customerDocumentType?: string;
  description?: string;
  postbackUrl?: string;
  organizationId: string;
  externalCode?: string;
  metadata?: Record<string, any>;
  idempotencyKey?: string; // Chave de idempotência opcional
}): Promise<any> {
  try {
    const {
      amount,
      customerName,
      customerEmail,
      customerPhone,
      customerDocument,
      customerDocumentType = "CPF",
      description,
      postbackUrl,
      organizationId,
      externalCode,
      metadata = {},
      idempotencyKey
    } = params;

    // Generate idempotency key if not provided
    const gatewayIdempotencyKey = idempotencyKey || `transfeera-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;

    // Get Transfeera credentials and access token
    const credentials = await getTransfeeraCredentials(organizationId);
    const { environment } = credentials;
    const accessToken = await getTransfeeraAccessToken(organizationId);
    const baseUrl = TRANSFEERA_API_BASE_URL[environment];

    logger.info("Creating PIX payment with Transfeera", {
      amount,
      customerEmail,
      organizationId,
      environment,
      baseUrl,
      hasAccessToken: !!accessToken,
      clientIdLength: credentials.clientId?.length || 0,
      clientSecretLength: credentials.clientSecret?.length || 0,
      idempotencyKey: gatewayIdempotencyKey
    });

    // Ensure we have a valid webhook URL for postbacks
    const webhookUrl = postbackUrl || `${process.env.NEXT_PUBLIC_SITE_URL}/api/webhooks/transfeera`;

    // Format customer document if provided
    let formattedDocument = customerDocument;
    if (customerDocument) {
      // Remove non-numeric characters for CPF/CNPJ
      formattedDocument = customerDocument.replace(/\D/g, '');
    }

    // Prepare request body for Transfeera PIX payment based on documentation
    const requestBody = {
      // Payment methods array with 'pix' as the only option
      payment_methods: ["pix"],

      // Payment method details with pix_key
      payment_method_details: {
        pix: {
          pix_key: "56881442000149" // Use a random key type for dynamic QR code generation
        }
      },

      // Payer information - always provide payer info even if document is not provided
      payer: {
        name: customerName || "Cliente não identificado", // Provide a fallback name
        tax_id: formattedDocument || "00000000000", // Use a placeholder CPF if not provided
        // Address is required and all fields must be filled
        address: {
          street: "Rua não informada",
          number: "S/N",
          district: "Centro",
          city: "São Paulo",
          state: "SP",
          postal_code: "01001000"
        }
      },

      // Amount in cents (Transfeera expects integer values)
      amount: Math.round(amount * 100),

      // Due date and expiration date in YYYY-MM-DD format
      due_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0], // YYYY-MM-DD format
      expiration_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0], // YYYY-MM-DD format

      // Description and external ID for tracking
      description: description || "Pagamento via Pix",
      external_id: externalCode || `orionpay-${Date.now()}`
    };

    // Log request details
    logger.info("Transfeera PIX charge request details", {
      url: `${baseUrl}/charges`,
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": `Bearer ${accessToken ? "***" : "missing"}`,
        "User-Agent": "OrionPay (<EMAIL>)"
      },
      body: JSON.stringify(requestBody)
    });

    // Create a PIX charge using Transfeera API
    const response = await fetch(`${baseUrl}/charges`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": `Bearer ${accessToken}`,
        "User-Agent": "OrionPay (<EMAIL>)"
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = await response.text();
      }

      // Log detailed error information
      logger.error("Error creating Transfeera PIX charge", {
        error: errorData,
        status: response.status,
        statusText: response.statusText,
        url: `${baseUrl}/charges`,
        organizationId,
        environment,
        hasAccessToken: !!accessToken,
        accessTokenLength: accessToken?.length || 0,
        responseHeaders: Object.fromEntries([...response.headers.entries()]),
      });

      // Check if it's a 404 error, which might indicate an incorrect API endpoint
      if (response.status === 404) {
        logger.error("Transfeera API endpoint not found (404)", {
          url: `${baseUrl}/charges`,
          environment,
          baseUrl
        });

        // Try to get more information about the API
        try {
          const infoResponse = await fetch(`${baseUrl}/info`, {
            method: "GET",
            headers: {
              "Accept": "application/json",
              "User-Agent": "OrionPay (<EMAIL>)"
            }
          });

          const infoData = infoResponse.ok ? await infoResponse.json() : await infoResponse.text();
          logger.info("Transfeera API info response", {
            status: infoResponse.status,
            data: infoData
          });
        } catch (infoError) {
          logger.error("Error getting Transfeera API info", {
            error: infoError instanceof Error ? infoError.message : String(infoError)
          });
        }
      }

      throw new Error(`Failed to create Transfeera PIX charge: ${JSON.stringify(errorData)}`);
    }

    const data = await response.json();
    // Log the complete response for debugging
    logger.info("Successfully created Transfeera PIX charge", {
      chargeId: data.id,
      status: data.status || "PENDING",
      organizationId,
      responseData: JSON.stringify(data)
    });

    // Extract QR code data from the response
    // According to the documentation, the QR code is inside the receivables array
    let qrCodeData = null;

    // Log the complete response structure for debugging
    logger.info("Transfeera response structure", {
      hasReceivables: !!data.receivables && Array.isArray(data.receivables),
      receivablesLength: data.receivables?.length || 0,
      firstReceivableHasQrcode: data.receivables?.[0]?.qrcode ? true : false,
      responseKeys: Object.keys(data)
    });

    // Try to extract QR code from receivables
    if (data.receivables && Array.isArray(data.receivables) && data.receivables.length > 0) {
      // Find the first receivable with a QR code
      const receivableWithQrCode = data.receivables.find((r: any) => r.qrcode);

      if (receivableWithQrCode?.qrcode) {
        qrCodeData = receivableWithQrCode.qrcode;
        logger.info("Found QR code in receivables", {
          receivableId: receivableWithQrCode.id,
          qrCodeData: JSON.stringify(qrCodeData)
        });
      } else {
        logger.warn("No QR code found in receivables", {
          chargeId: data.id,
          receivablesCount: data.receivables.length
        });
      }
    } else if (data.id && !data.qrcode) {
      // If no receivables with QR code, try to fetch it separately
      try {
        logger.info("QR code not found in receivables, fetching QR code data", {
          chargeId: data.id
        });

        // Make an additional API call to get the QR code
        const qrCodeResponse = await fetch(`${baseUrl}/charges/${data.id}/qrcode`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": `Bearer ${accessToken}`,
            "User-Agent": "OrionPay (<EMAIL>)"
          }
        });

        if (qrCodeResponse.ok) {
          qrCodeData = await qrCodeResponse.json();
          logger.info("Successfully fetched QR code data", {
            chargeId: data.id,
            qrCodeData: JSON.stringify(qrCodeData)
          });
        } else {
          logger.error("Failed to fetch QR code data", {
            chargeId: data.id,
            status: qrCodeResponse.status,
            statusText: qrCodeResponse.statusText
          });
        }
      } catch (qrCodeError) {
        logger.error("Error fetching QR code data", {
          chargeId: data.id,
          error: qrCodeError instanceof Error ? qrCodeError.message : "Unknown error"
        });
      }
    }

    // Get gateway ID for reference
    const gatewayId = (await db.paymentGateway.findFirst({
      where: {
        type: "TRANSFEERA",
        isActive: true,
        canReceive: true
      },
      orderBy: { priority: 'asc' }
    }))?.id;

    // Check if a transaction with this external reference already exists
    let existingDbTransaction = null;
    if (externalCode) {
      existingDbTransaction = await db.transaction.findFirst({
        where: {
          referenceCode: externalCode,
          organizationId,
          type: "CHARGE"
        }
      });
    }

    // If no existing transaction found, create a new one
    if (!existingDbTransaction) {
      await db.transaction.create({
        data: {
          externalId: data.id.toString(),
          referenceCode: externalCode,
          customerName,
          customerEmail,
          customerPhone: customerPhone || "",
          customerDocument: formattedDocument || "",
          customerDocumentType: customerDocumentType || "CPF",
          amount,
          status: "PENDING",
          type: "CHARGE",
          description: description || "Pagamento via Pix",
          metadata: {
            ...metadata,
            pixData: {
              qrCode: qrCodeData?.emv_payload || "", // EMV payload from documentation or additional API call
              qrCodeImage: "", // Transfeera doesn't provide QR code image URL
              expiresAt: data.receivables?.[0]?.expiration_date || data.expiration_date
            },
            idempotencyKey: gatewayIdempotencyKey,
            originalResponse: JSON.stringify(data).substring(0, 500)
          },
          organizationId,
          gatewayId
        }
      });

      logger.info("Created new transaction record in database", {
        externalId: data.id,
        customerEmail,
        amount
      });
    } else {
      // Update existing transaction with new PIX data
      await db.transaction.update({
        where: { id: existingDbTransaction.id },
        data: {
          externalId: data.id.toString(),
          status: "PENDING",
          metadata: {
            ...(existingDbTransaction.metadata as any || {}),
            pixData: {
              qrCode: qrCodeData?.emv_payload || "", // EMV payload from documentation or additional API call
              qrCodeImage: "", // Transfeera doesn't provide QR code image URL
              expiresAt: data.receivables?.[0]?.expiration_date || data.expiration_date
            },
            idempotencyKey: gatewayIdempotencyKey,
            originalResponse: JSON.stringify(data).substring(0, 500),
            updatedAt: new Date().toISOString()
          }
        }
      });

      logger.info("Updated existing transaction with new PIX data", {
        transactionId: existingDbTransaction.id,
        externalId: data.id
      });
    }

    // Return the PIX payment data
    return {
      id: data.id,
      status: (data.status || "pending").toLowerCase(),
      pix: {
        payload: qrCodeData?.emv_payload || "", // EMV payload from documentation or additional API call
        encodedImage: "", // Transfeera doesn't provide QR code image URL
        expirationDate: data.receivables?.[0]?.expiration_date || data.expiration_date
      },
      // Include additional fields that might be needed by the client
      pixCode: qrCodeData?.emv_payload || "",
      pixQrCode: "",
      originalResponse: JSON.stringify(data).substring(0, 200)
    };
  } catch (error) {
    logger.error("Error in Transfeera createPixCharge", {
      error: error instanceof Error ? error.message : "Unknown error",
      organizationId: params.organizationId
    });
    throw error;
  }
}

// Process a Pix withdrawal (transfer)
export async function processPixWithdrawal(params: {
  amount: number;
  pixKey: string;
  pixKeyType: string;
  postbackUrl?: string;
  organizationId: string;
  recurrence?: {
    interval: number;
    intervalType: 'day' | 'week' | 'month';
    numberOfPayments: number;
  };
  pixEndToEndId?: string; // Optional End-to-End ID for tracking
  transactionId?: string; // Add transaction ID parameter
}): Promise<any> {
  try {
    const { amount, pixKey, pixKeyType, postbackUrl, organizationId, recurrence, pixEndToEndId, transactionId } = params;

    const { environment } = await getTransfeeraCredentials(organizationId);
    const accessToken = await getTransfeeraAccessToken(organizationId);
    const baseUrl = TRANSFEERA_API_BASE_URL[environment];

    // Map our pixKeyType to Transfeera's format
    const transfeeraKeyType = mapPixKeyTypeToTransfeera(pixKeyType);

    // Format the PIX key based on the key type
    let formattedPixKey = pixKey;
    if (pixKeyType.toLowerCase() === 'phone') {
      // Check if we already have a "+55" at the beginning
      if (pixKey.startsWith('+55')) {
        // Already has proper format, just keep it as is
        formattedPixKey = pixKey;

        // Ensure it follows the pattern +55DDNNNNNNNNN without extra characters
        const digitsOnly = pixKey.replace(/\D/g, '');
        if (digitsOnly.length >= 12) { // At least country code (2) + area code (2) + number (8+)
          formattedPixKey = `+${digitsOnly}`;
        }
      } else {
        // Remove any non-numeric characters
        formattedPixKey = pixKey.replace(/\D/g, '');

        // Check if it already starts with 55 (country code without +)
        if (formattedPixKey.startsWith('55') && formattedPixKey.length >= 12) {
          // Add + to properly format
          formattedPixKey = `+${formattedPixKey}`;
        } else if (formattedPixKey.length <= 11) {
          // For Brazilian numbers without country code, add +55
          formattedPixKey = `+55${formattedPixKey}`;
        } else {
          // For other numbers, just add +
          formattedPixKey = `+${formattedPixKey}`;
        }
      }

      logger.info("Formatting phone PIX key", {
        original: pixKey,
        formatted: formattedPixKey,
        keyType: transfeeraKeyType
      });
    } else if (pixKeyType.toLowerCase() === 'cpf' || pixKeyType.toLowerCase() === 'cnpj') {
      // Remove all non-digit characters for CPF/CNPJ
      formattedPixKey = pixKey.replace(/\D/g, '');
    }

    // Buscar o CNPJ da organização na tabela organization_legal_info
    const organizationLegalInfo = await db.organizationLegalInfo.findUnique({
      where: { organizationId },
      select: { document: true, documentType: true }
    });

    let organizationCnpj = "56881442000149"; // CPF padrão como fallback

    if (organizationLegalInfo?.document && organizationLegalInfo.documentType === "CNPJ") {
      // Remover caracteres não numéricos do CNPJ
      organizationCnpj = organizationLegalInfo.document.replace(/\D/g, '');
      logger.info("CNPJ da organização encontrado para validação", {
        organizationId,
        cnpj: organizationCnpj
      });
    } else {
      logger.warn("CNPJ da organização não encontrado ou não é CNPJ, usando valor padrão", {
        organizationId,
        documentType: organizationLegalInfo?.documentType,
        hasDocument: !!organizationLegalInfo?.document
      });
    }

    // Primeiro, precisamos criar um lote (batch) para a transferência
    logger.info("Creating Transfeera batch for transfer", { organizationId });

    const batchResponse = await fetch(`${baseUrl}/batch`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": `Bearer ${accessToken}`,
        "User-Agent": "OrionPay (<EMAIL>)"
      },
      body: JSON.stringify({
        name: `PIX Transfer ${new Date().toISOString()}`
      }),
    });

    if (!batchResponse.ok) {
      let errorData;
      try {
        errorData = await batchResponse.json();
      } catch (e) {
        errorData = await batchResponse.text();
      }

      logger.error("Error creating Transfeera batch", {
        error: errorData,
        status: batchResponse.status,
        organizationId
      });
      throw new Error(`Failed to create Transfeera batch: ${JSON.stringify(errorData)}`);
    }

    const batchData = await batchResponse.json();
    const batchId = batchData.id;

    logger.info("Successfully created Transfeera batch", { batchId, organizationId });

    // Create an integration_id that includes the transaction ID if available
    const integrationId = transactionId ?
      `orionpay-tx-${transactionId}-${Date.now()}` :
      `orionpay-${Date.now()}`;

    // Armazenar o integrationId nos metadados da transação, se disponível
    if (transactionId) {
      try {
        const transaction = await db.transaction.findUnique({
          where: { id: transactionId }
        });

        if (transaction) {
          await db.transaction.update({
            where: { id: transactionId },
            data: {
              metadata: {
                ...(transaction.metadata as any || {}),
                integrationId,
                batchId
              }
            }
          });

          logger.info("Atualizado integrationId nos metadados da transação", {
            transactionId,
            integrationId,
            batchId
          });
        }
      } catch (error) {
        logger.warn("Erro ao atualizar integrationId nos metadados da transação", {
          error: error instanceof Error ? error.message : "Erro desconhecido",
          transactionId,
          integrationId
        });
        // Continuar mesmo com erro
      }
    }

    // Agora, criar a transferência dentro do lote
    const response = await fetch(`${baseUrl}/batch/${batchId}/transfer`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": `Bearer ${accessToken}`,
        "User-Agent": "OrionPay (<EMAIL>)"
      },
      body: JSON.stringify({
        value: amount,
        integration_id: integrationId,
        idempotency_key: `orionpay-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`,
        pix_description: "Transferência via Pix",
        ...(pixEndToEndId && { pix_end2end_id: pixEndToEndId }),
        destination_bank_account: {
          pix_key_type: transfeeraKeyType,
          pix_key: formattedPixKey,
          email: pixKeyType.toLowerCase() === 'email' ? formattedPixKey : "", // Use o próprio email se for chave tipo email
        },
        // Adicionar validação de CPF/CNPJ quando apropriado
        pix_key_validation: {
          // Sempre fornece o CNPJ da organização para validação
          cpf_cnpj: null
        },
        // Adicionar recorrência se necessário
        ...(recurrence && {
          recurrence: {
            interval: recurrence.interval,
            interval_type: recurrence.intervalType,
            number_of_payments: recurrence.numberOfPayments
          }
        }),
      }),
    });

    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = await response.text();
      }

      logger.error("Error processing Transfeera Pix withdrawal", {
        error: errorData,
        status: response.status,
        organizationId,
        batchId
      });

      // Enhanced error handling for Transfeera responses
      let errorMsg = `Failed to process Transfeera Pix withdrawal: ${JSON.stringify(errorData)}`;

      // Check if error data contains status and status_description fields
      if (errorData && typeof errorData === 'object') {
        if (errorData.status === 'DEVOLVIDO' || errorData.status === 'devolvido') {
          // Special handling for "DEVOLVIDO" status
          const statusDesc = errorData.status_description ||
                            (errorData.error && errorData.error.message) ||
                            'Transferência devolvida pelo banco';
          const errorCode = (errorData.error && errorData.error.code) || 'transfer_returned';

          logger.warn(`Transferência devolvida pelo banco: ${statusDesc}`, {
            status: errorData.status,
            code: errorCode,
            description: statusDesc,
            transferId: errorData.id,
            batchId
          });

          // Throw with detailed information for better client handling
          throw {
            message: errorMsg,
            response: {
              data: errorData,
              status: response.status
            },
            status: 'CANCELED',
            description: statusDesc,
            code: errorCode
          };
        }
      }

      throw new Error(errorMsg);
    }

    const data = await response.json();
    logger.info("Successfully created Transfeera transfer", {
      transferId: data.id,
      batchId,
      status: data.status,
      organizationId,
      responseData: JSON.stringify(data).substring(0, 500)
    });

    // Verificar se o ID da transferência existe e é válido
    if (!data.id) {
      logger.error("Transfeera transfer created but missing ID", {
        data: JSON.stringify(data).substring(0, 500),
        batchId,
        organizationId
      });
      throw new Error("Transfeera transfer created but missing ID in response");
    }

    // Garantir que temos um ID como string
    const transferId = data.id.toString();

    // Fechar o lote para iniciar o processamento
    logger.info("Closing Transfeera batch to initiate processing", {
      batchId,
      organizationId
    });

    try {
      // Fechar o lote
      await closeBatch(batchId, accessToken, baseUrl);
      logger.info("Successfully closed Transfeera batch", {
        batchId,
        organizationId
      });

      // Aguardar um momento para que a transferência seja processada
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Se temos o ID da transação interna, atualizá-la diretamente
      if (transactionId) {
        const transaction = await db.transaction.findUnique({
          where: { id: transactionId }
        });

        if (transaction) {
          await db.transaction.update({
            where: { id: transactionId },
            data: {
              externalId: transferId,
              metadata: {
                ...(transaction.metadata as any || {}),
                transferId,
                batchId,
                transferCreatedAt: new Date().toISOString(),
                transferStatus: data.status || "CREATED"
              }
            }
          });

          logger.info("Transação atualizada com externalId e dados da transferência", {
            transactionId,
            externalId: transferId,
            batchId
          });
        }
      } else {
        // Buscar transação pelo integrationId ou criar outro mecanismo de vinculação
        // Obter a transação recentemente criada pelo referenceId
        const createdTransaction = await db.transaction.findFirst({
          where: {
            metadata: {
              path: ['integrationId'],
              equals: integrationId
            },
            type: "SEND",
            organizationId
          },
          orderBy: {
            createdAt: 'desc'
          }
        });

        // Se encontrou, atualizar
        if (createdTransaction) {
          await db.transaction.update({
            where: { id: createdTransaction.id },
            data: {
              externalId: transferId,
              metadata: {
                ...(createdTransaction.metadata as any || {}),
                transferId,
                batchId,
                transferCreatedAt: new Date().toISOString(),
                transferStatus: data.status || "CREATED"
              }
            }
          });

          logger.info("Transação encontrada e atualizada via integrationId", {
            transactionId: createdTransaction.id,
            externalId: transferId,
            integrationId
          });
        } else {
          // Se não encontrou pelo integrationId, tenta pelo mais recente...
          logger.warn("Não foi possível encontrar a transação via integrationId", {
            integrationId,
            transferId,
            organizationId
          });
        }
      }

      // Usar a nova função de sincronização para verificar o status e atualizar o saldo
      try {
        const syncResult = await syncTransferStatus({
          transferId,
          organizationId,
          batchId,
          transactionId,
          forceApproved: false
        });

        logger.info("Sincronização de status após fechamento de lote", {
          result: syncResult,
          transferId,
          transactionInternalId: transactionId,
          organizationId
        });

        // Agendar sincronizações adicionais para garantir que o status seja atualizado
        // Isso é importante porque o status pode não ser atualizado imediatamente
        const scheduleSync = async (delay: number) => {
          logger.info(`Scheduling status sync in ${delay / 1000} seconds`, {
            transferId,
            transactionId
          });

          setTimeout(async () => {
            try {
              const laterSyncResult = await syncTransferStatus({
                transferId,
                organizationId,
                batchId,
                transactionId,
                forceApproved: false
              });

              logger.info(`Sincronização programada após ${delay}ms`, {
                result: laterSyncResult,
                transferId,
                transactionId
              });
            } catch (error) {
              logger.warn(`Erro na sincronização programada após ${delay}ms`, {
                error: error instanceof Error ? error.message : "Erro desconhecido",
                transferId,
                transactionId
              });
            }
          }, delay);
        };

        // Executar sincronizações adicionais após 5s e 15s
        scheduleSync(5000); // após 5 segundos
        scheduleSync(15000); // após 15 segundos
        scheduleSync(30000); // adicionar mais uma verificação após 30 segundos
      } catch (syncError) {
        // Se houver erro na sincronização, apenas logar e continuar
        // O status será atualizado posteriormente via verificação manual
        const errorInfo = {
          transferId,
          organizationId,
          errorMessage: syncError instanceof Error ? syncError.message : "Erro desconhecido",
          errorName: syncError instanceof Error ? syncError.name : "UnknownError",
          errorStack: syncError instanceof Error ? (syncError.stack?.split('\n')[0] || "") : ""
        };

        logger.warn("Erro ao sincronizar status após fechamento de lote", errorInfo);
      }
    } catch (closeError) {
      const errorInfo = {
        batchId,
        organizationId,
        transferId,
        errorMessage: closeError instanceof Error ? closeError.message : "Erro desconhecido",
        errorName: closeError instanceof Error ? closeError.name : "UnknownError",
        errorStack: closeError instanceof Error ? (closeError.stack?.split('\n')[0] || "") : ""
      };

      logger.error("Error closing Transfeera batch", errorInfo);
      // Continue even if batch closing fails, as we'll handle it in the webhook
    }

    // Get gateway ID for reference
    const gatewayId = (await db.paymentGateway.findFirst({
      where: {
        type: "TRANSFEERA",
        isActive: true,
        canSend: true
      },
      orderBy: { priority: 'asc' }
    }))?.id;

    return {
      id: transferId,
      batchId,
      status: mapTransfeeraStatusToInternal(data.status || "PROCESSING"),
      gatewayId,
      originalResponse: JSON.stringify(data).substring(0, 200) // Adicionar parte da resposta original para debug
    };
  } catch (error) {
    // Safely log error information without passing the full error object
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorName = error instanceof Error ? error.name : 'Error';

    logger.error("Error in Transfeera processPixWithdrawal", {
      errorMessage,
      errorName,
      pixKeyType: params?.pixKeyType,
      organizationId: params?.organizationId
    });
    throw error;
  }
}

// Get transaction status
export async function getTransactionStatus(params: {
  transactionId: string;
  organizationId: string;
}): Promise<any> {
  try {
    const { transactionId, organizationId } = params;

    logger.info("Transfeera getTransactionStatus called", { transactionId, organizationId });

    // Verificar se o transactionId é um ID externo da Transfeera (numérico)
    const isExternalId = /^\d+$/.test(transactionId);
    let externalId = isExternalId ? transactionId : null;
    let transaction = null;

    // Se não for um ID externo, buscar a transação no banco de dados
    if (!isExternalId) {
      // Get the transaction from our database
      transaction = await db.transaction.findUnique({
        where: { id: transactionId },
      });

      if (!transaction) {
        logger.error("Transaction not found in Transfeera getTransactionStatus", { transactionId });
        throw new Error(`Transaction ${transactionId} not found`);
      }

      // Se não tiver externalId, podemos retornar o status atual
      // Isso é útil para transações que ainda não foram processadas pelo gateway
      if (!transaction.externalId) {
        logger.warn("Transaction has no external ID in Transfeera getTransactionStatus", {
          transactionId,
          currentStatus: transaction.status
        });

        return {
          success: true,
          status: transaction.status,
          mappedStatus: transaction.status,
          message: "Transaction has no external ID yet"
        };
      }

      externalId = transaction.externalId;
    }

    logger.info("Using external ID for Transfeera status check", {
      originalId: transactionId,
      externalId,
      isExternalId
    });

    const { environment } = await getTransfeeraCredentials(organizationId);
    const accessToken = await getTransfeeraAccessToken(organizationId);
    const baseUrl = TRANSFEERA_API_BASE_URL[environment];

    logger.info("Transfeera credentials obtained", {
      environment,
      baseUrl,
      hasAccessToken: !!accessToken
    });

    // Determine the correct endpoint based on transaction type
    let endpoint;
    if (transaction && transaction.type === "CHARGE") {
      // For CHARGE transactions, use the PIX charge endpoint
      endpoint = `${baseUrl}/pix/charge/${externalId}`;
      logger.info("Checking status of a CHARGE transaction with Transfeera", {
        transactionId,
        organizationId,
        endpoint
      });
    } else {
      // For SEND/transfer transactions, use the transfer endpoint
      endpoint = `${baseUrl}/transfer/${externalId}`;
      logger.info("Checking status of a SEND transaction with Transfeera", {
        transactionId,
        organizationId,
        endpoint
      });
    }

    logger.info("Requesting Transfeera transfer status", { endpoint, externalId });

    // Get the transaction status from Transfeera
    const response = await fetch(endpoint, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${accessToken}`,
        "Accept": "application/json",
        "Content-Type": "application/json",
        "User-Agent": "OrionPay (<EMAIL>)"
      },
    });

    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = await response.text();
      }

      logger.error("Error getting Transfeera transaction status", {
        error: errorData,
        status: response.status,
        statusText: response.statusText,
        transactionId,
        organizationId
      });

      // Se o erro for 404, pode ser que a transação ainda não tenha sido processada
      // Nesse caso, retornamos o status atual
      if (response.status === 404) {
        logger.warn("Transaction not found in Transfeera API, returning current status", {
          transactionId,
          externalId,
          currentStatus: transaction ? transaction.status : 'UNKNOWN'
        });

        return {
          success: true,
          status: transaction ? transaction.status : 'PENDING',
          mappedStatus: transaction ? transaction.status : 'PENDING',
          message: "Transaction not found in Transfeera API yet"
        };
      }

      throw new Error(`Failed to get Transfeera transaction status: ${JSON.stringify(errorData)}`);
    }

    const data = await response.json();
    logger.info("Transfeera transfer status response", { data });

    // Verificar especificamente se o status é FINALIZADO antes de normalizar
    if (data.status === "FINALIZADO") {
      logger.info("Status FINALIZADO detectado diretamente da Transfeera, forçando mapeamento para APPROVED", {
        originalStatus: data.status
      });

      return {
        success: true,
        status: "APPROVED",
        mappedStatus: "APPROVED",
        originalStatus: data.status,
        data
      };
    }

    const status = mapTransfeeraStatusToInternal(data.status);
    logger.info("Mapped Transfeera status", {
      originalStatus: data.status,
      mappedStatus: status
    });

    // Adicionar verificação adicional após o mapeamento para garantir
    // que estados de sucesso da Transfeera não sejam perdidos
    if (data.status &&
       (data.status.toUpperCase() === "FINALIZADO" ||
        data.status.toLowerCase() === "finalizado" ||
        data.status.toLowerCase().includes("concluido") ||
        data.status.toLowerCase().includes("concluida") ||
        data.status.toLowerCase().includes("pago") ||
        data.status.toLowerCase().includes("aprovado"))) {

      logger.info("Status de sucesso identificado pela verificação adicional", {
        originalStatus: data.status,
        mappedStatus: "APPROVED"
      });

      return {
        success: true,
        status: "APPROVED",
        mappedStatus: "APPROVED",
        originalStatus: data.status,
        fallbackApplied: true,
        data
      };
    }

    return {
      success: true,
      status, // The mapped status (standard internal format)
      mappedStatus: status, // The same as above, but explicitly marked for consumers
      originalStatus: data.status, // Original status from provider
      data // Full response data
    };
  } catch (error) {
    const errorInfo = {
      transactionId: params.transactionId,
      errorMessage: error instanceof Error ? error.message : "Erro desconhecido",
      errorName: error instanceof Error ? error.name : "UnknownError",
      errorStack: error instanceof Error ? (error.stack?.split('\n')[0] || "") : ""
    };

    logger.error("Error getting transaction status", errorInfo);
    throw error;
  }
}

// Helper function to map Transfeera status to our internal status
function mapTransfeeraStatusToInternal(transfeeraStatus: string): string {
  if (!transfeeraStatus) return "PROCESSING";

  // Normalizar o status para minúsculas e remover acentos
  const normalizedStatus = transfeeraStatus.toLowerCase()
    .normalize("NFD")
    .replace(/[\u0300-\u036f]/g, "");

  // Log para debug
  logger.info("Normalizando status da Transfeera", {
    original: transfeeraStatus,
    normalized: normalizedStatus
  });

  switch (normalizedStatus) {
    // Status de pendente
    case "pending":
    case "waiting":
    case "created":
    case "criada":
    case "aguardando":
    case "pendente":
      return "PENDING";

    // Status de aprovado
    case "approved":
    case "completed":
    case "paid":
    case "finished":
    case "done":
    case "finalizado":
    case "finalizada":
    case "concluido":
    case "concluida":
    case "aprovado":
    case "aprovada":
    case "pago":
    case "paga":
    case "recebido":
    case "received":
      return "APPROVED";

    // Status de rejeitado
    case "rejected":
    case "failed":
    case "error":
    case "erro":
    case "falha":
    case "rejeitado":
    case "rejeitada":
      return "REJECTED";

    // Status de cancelado
    case "canceled":
    case "cancelled":
    case "cancelado":
    case "cancelada":
    case "devolvido":
    case "devolvida":
      return "CANCELED";

    // Status de processamento
    case "processing":
    case "in_progress":
    case "scheduled":
    case "processando":
    case "em_processamento":
    case "em_andamento":
    case "agendado":
    case "agendada":
      return "PROCESSING";

    // Status de reembolso
    case "refunded":
    case "reembolsado":
    case "reembolsada":
    case "estornado":
    case "estornada":
      return "REFUNDED";

    default:
      logger.warn("Status desconhecido da Transfeera", { status: transfeeraStatus });
      return "PROCESSING";
  }
}

// Helper function to map our pixKeyType to Transfeera's format
function mapPixKeyTypeToTransfeera(pixKeyType: string): string {
  switch (pixKeyType.toLowerCase()) {
    case "cpf":
      return "CPF";
    case "cnpj":
      return "CNPJ";
    case "email":
      return "EMAIL";
    case "phone":
      return "TELEFONE";
    case "random":
    case "evp":
      return "CHAVE_ALEATORIA";
    default:
      return pixKeyType.toUpperCase();
  }
}

// Helper function to map pixKeyType to database enum values
function mapToDbPixKeyType(pixKeyType: string): 'CPF' | 'CNPJ' | 'EMAIL' | 'PHONE' | 'RANDOM' {
  switch (pixKeyType.toLowerCase()) {
    case "cpf":
      return "CPF";
    case "cnpj":
      return "CNPJ";
    case "email":
      return "EMAIL";
    case "phone":
      return "PHONE";
    case "random":
    case "evp":
      return "RANDOM";
    default:
      return "RANDOM"; // Fallback to RANDOM
  }
}

// Helper function to close a batch
async function closeBatch(batchId: string, accessToken: string, baseUrl: string): Promise<any> {
  try {
    logger.info("Iniciando fechamento de lote na Transfeera", { batchId });

    const response = await fetch(`${baseUrl}/batch/${batchId}/close`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": `Bearer ${accessToken}`,
        "User-Agent": "OrionPay (<EMAIL>)"
      },
    });

    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = await response.text();
      }

      logger.error("Erro ao fechar lote na Transfeera", {
        error: errorData,
        status: response.status,
        batchId
      });

      throw new Error(`Failed to close Transfeera batch: ${JSON.stringify(errorData)}`);
    }

    const data = await response.json();
    logger.info("Lote fechado com sucesso na Transfeera", {
      batchId,
      responseData: data
    });

    // Verificar o status retornado pelo fechamento do lote
    if (data && data.status) {
      logger.info("Status do lote após fechamento", {
        batchId,
        status: data.status,
        success: data.status === "approved" || data.status === "processing"
      });

      // Se o status for aprovado ou em processamento, já marcar transferências para atualização imediata
      if (data.status === "approved" || data.status === "processing") {
        try {
          logger.info("Marcando transferências do lote para processamento imediato", { batchId });

          // Buscar todas as transferências associadas ao lote na Transfeera
          // Este endpoint retorna todas as transferências do lote
          const transfersResponse = await fetch(`${baseUrl}/batch/${batchId}/transfer`, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              "Accept": "application/json",
              "Authorization": `Bearer ${accessToken}`,
              "User-Agent": "OrionPay (<EMAIL>)"
            },
          });

          if (transfersResponse.ok) {
            const transfersData = await transfersResponse.json();
            logger.info("Transferências do lote obtidas com sucesso", {
              batchId,
              count: transfersData.length || 0
            });

            // Processar cada transferência do lote
            if (Array.isArray(transfersData) && transfersData.length > 0) {
              // Extrair organizationId de alguma transação existente relacionada ao lote
              const existingTransaction = await db.transaction.findFirst({
                where: {
                  metadata: {
                    path: ['batchId'],
                    equals: batchId
                  }
                },
                select: {
                  organizationId: true
                }
              });

              const organizationId = existingTransaction?.organizationId;

              if (organizationId) {
                logger.info("Organização identificada para processamento de transferências", {
                  batchId,
                  organizationId,
                  transferCount: transfersData.length
                });

                // Para cada transferência, criar uma tarefa assíncrona para evitar bloqueio
                transfersData.forEach((transfer: any) => {
                  // Executar em um setTimeout para não bloquear o fluxo principal
                  setTimeout(async () => {
                    try {
                      // Verificar se o ID da transferência é válido
                      if (transfer.id) {
                        logger.info("Processando transferência do lote fechado", {
                          batchId,
                          transferId: transfer.id,
                          status: transfer.status
                        });

                        // Buscar a transação correspondente
                        const transaction = await db.transaction.findFirst({
                          where: {
                            externalId: transfer.id.toString(),
                            organizationId
                          }
                        });

                        // Se encontrou a transação, sincronizar status
                        if (transaction) {
                          logger.info("Transação encontrada para transferência do lote", {
                            transferId: transfer.id,
                            transactionId: transaction.id,
                            status: transaction.status
                          });

                          // Verificar se precisa ser atualizada (se não for um status final)
                          if (!["APPROVED", "REJECTED", "CANCELED"].includes(transaction.status)) {
                            // Atualizar status via syncTransferStatus
                            await syncTransferStatus({
                              transferId: transfer.id.toString(),
                              organizationId,
                              batchId,
                              transactionId: transaction.id
                            });
                          }
                        } else {
                          logger.warn("Transação não encontrada para transferência do lote", {
                            transferId: transfer.id,
                            batchId,
                            organizationId
                          });
                        }
                      }
                    } catch (transferError) {
                      logger.error("Erro ao processar transferência individual do lote", {
                        error: transferError,
                        transferId: transfer?.id,
                        batchId
                      });
                    }
                  }, 0);
                });
              } else {
                logger.warn("Não foi possível identificar a organização para o lote", { batchId });
              }
            }
          } else {
            logger.warn("Falha ao obter transferências do lote fechado", {
              batchId,
              status: transfersResponse.status
            });
          }
        } catch (processingError) {
          logger.error("Erro ao processar transferências após fechamento de lote", {
            error: processingError,
            batchId
          });
          // Não interromper o fluxo por causa de erros na atualização de status
        }
      }
    }

    return data;
  } catch (error) {
    logger.error("Erro no fechamento de lote", { error, batchId });
    throw error;
  }
}

/**
 * Sincroniza o status de uma transferência Transfeera e atualiza o saldo
 * Esta função é útil para ser chamada após o fechamento de lote
 */
export async function syncTransferStatus(params: {
  transferId: string;
  organizationId: string;
  batchId?: string;
  transactionId?: string; // ID interno opcional da transação
  forceApproved?: boolean; // Parâmetro para forçar o status de aprovação
}): Promise<any> {
  try {
    const { transferId, organizationId, batchId, transactionId, forceApproved } = params;

    logger.info("Sincronizando status de transferência Transfeera", {
      transferId,
      transactionId,
      organizationId,
      batchId,
      forceApproved
    });

    // Log adicional para depuração do ID externo
    logger.debug("Verificando associação entre ID externo e transação", {
      transferId,
      transactionId,
      hasTransactionId: !!transactionId
    });

    // Adicionando log para depuração

    // Importar função de atualização de saldo
    const { updateOrganizationBalance, BalanceOperationType } = await import("../../src/balance/balance-service");
    logger.debug(`Iniciando syncTransferStatus para transferId: ${transferId}, transactionId: ${transactionId}`);

    // If we have a direct transaction ID, use it first
    let transaction = null;
    let searchMethod = "";

    if (transactionId) {
      transaction = await db.transaction.findUnique({
        where: {
          id: transactionId,
          organizationId
        }
      });

      if (transaction) {
        searchMethod = "direct_id";
        logger.info("Transação encontrada diretamente pelo ID", {
          transactionId,
          currentExternalId: transaction.externalId
        });

        // Update externalId if it's not already set or is different
        if (!transaction.externalId || transaction.externalId !== transferId) {
          logger.info("Atualizando ID externo da transação", {
            transactionId: transaction.id,
            oldExternalId: transaction.externalId,
            newExternalId: transferId
          });

          const existingMetadata = transaction.metadata || {};
          const updatedMetadata = Object.assign({}, existingMetadata, {
            transferId: transferId,
            transferIdMappedAt: new Date().toISOString(),
            originalExternalId: transaction.externalId || null
          });

          transaction = await db.transaction.update({
            where: { id: transaction.id },
            data: {
              externalId: transferId,
              metadata: updatedMetadata
            }
          });

          logger.info("Atualizado externalId da transação", {
            transactionId,
            externalId: transferId
          });
        }
      }
    }

    // If not found with direct ID, continue with other search methods
    if (!transaction) {
      // Estratégias de busca de transação:
      // 1. Pelo integration_id (que pode conter o ID da transação) - PRIORIDADE
      // 2. Pelo externalId (ID da Transfeera)
      // 3. Pelo metadata.transferId
      // 4. Pelo metadata.externalId

      // Buscar primeiro pelo integration_id que contém o transactionId
      if (transactionId) {
        transaction = await db.transaction.findFirst({
          where: {
            type: "SEND",
            organizationId,
            metadata: {
              path: ['integrationId'],
              string_contains: transactionId
            }
          }
        });

        if (transaction) {
          searchMethod = "integration_id_priority";
          logger.info("Transação encontrada por integrationId contendo o ID da transação (busca prioritária)", {
            transactionId,
            integrationId: (transaction.metadata as any)?.integrationId
          });

          // Atualizar externalId se necessário
          if (!transaction.externalId || transaction.externalId !== transferId) {
            const existingMetadata = transaction.metadata || {};
            const updatedMetadata = Object.assign({}, existingMetadata, {
              transferId: transferId,
              transferIdMappedAt: new Date().toISOString(),
              originalExternalId: transaction.externalId || null
            });

            transaction = await db.transaction.update({
              where: { id: transaction.id },
              data: {
                externalId: transferId,
                metadata: updatedMetadata
              }
            });
          }
        }
      }

      // New search method - check by integration_id pattern
      if (!transaction && transactionId) {
        transaction = await db.transaction.findFirst({
          where: {
            type: "SEND",
            organizationId,
            metadata: {
              path: ['integrationId'],
              string_contains: transactionId
            }
          }
        });

        if (transaction) {
          searchMethod = "integration_id";
          logger.info("Transação encontrada por integrationId contendo o ID da transação", {
            transactionId,
            integrationId: (transaction.metadata as any)?.integrationId
          });
        }
      }

      // Check by externalId
      if (!transaction) {
        transaction = await db.transaction.findFirst({
          where: {
            externalId: transferId,
            type: "SEND",
            organizationId
          }
        });

        if (transaction) {
          searchMethod = "externalId";
          logger.info("Transação encontrada pelo externalId", {
            externalId: transferId,
            transactionId: transaction.id
          });
        }
      }

      // Check by metadata.transferId
      if (!transaction) {
        transaction = await db.transaction.findFirst({
          where: {
            type: "SEND",
            organizationId,
            metadata: {
              path: ['transferId'],
              equals: transferId
            }
          }
        });

        if (transaction) {
          searchMethod = "metadata.transferId";
          logger.info("Transação encontrada por metadata.transferId", {
            transferId,
            transactionId: transaction.id
          });

          // Update externalId if needed
          if (!transaction.externalId || transaction.externalId !== transferId) {
            const existingMetadata = transaction.metadata || {};
            const updatedMetadata = Object.assign({}, existingMetadata, {
              transferIdMappedAt: new Date().toISOString(),
              originalExternalId: transaction.externalId
            });

            transaction = await db.transaction.update({
              where: { id: transaction.id },
              data: {
                externalId: transferId,
                metadata: updatedMetadata
              }
            });
          }
        }
      }

      // Last resort - find recent transaction without externalId
      if (!transaction) {
        // Only look at recent transactions (last 5 minutes)
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);

        transaction = await db.transaction.findFirst({
          where: {
            type: "SEND",
            organizationId,
            externalId: null,
            status: "PENDING",
            createdAt: {
              gte: fiveMinutesAgo
            }
          },
          orderBy: {
            createdAt: "desc"
          }
        });

        if (transaction) {
          searchMethod = "recent_without_externalId";
          logger.info("Transação encontrada por ser a mais recente sem externalId", {
            transferId,
            transactionId: transaction.id,
            createdAt: transaction.createdAt
          });

          // Update with the externalId
          const existingMetadata = transaction.metadata || {};
          const updatedMetadata = Object.assign({}, existingMetadata, {
            transferIdMappedAt: new Date().toISOString(),
            mappingMethod: "most_recent_without_id"
          });

          transaction = await db.transaction.update({
            where: { id: transaction.id },
            data: {
              externalId: transferId,
              metadata: updatedMetadata
            }
          });
        }
      }
    }

    if (!transaction) {
      logger.warn("Transação não encontrada para sincronização", {
        transferId,
        transactionId,
        organizationId
      });

      return {
        success: false,
        message: "Transação não encontrada"
      };
    }

    // Get transaction status from Transfeera
    let statusResult: any = { status: "PENDING", originalStatus: "pending" };

    // If forceApproved is true, skip API check and force APPROVED status
    if (forceApproved) {
      logger.info("Forçando status APPROVED para transferência", {
        transferId,
        transactionId: transaction.id,
        currentStatus: transaction.status
      });

      statusResult = {
        status: "APPROVED",
        mappedStatus: "APPROVED",
        originalStatus: "approved",
        forced: true
      };
    } else {
      // Check normal status via API
      try {
        statusResult = await getTransactionStatus({
          transactionId: transferId,
          organizationId
        });

        logger.info("Status obtido do gateway", { statusResult });

        // Check if statusResult indicates FINALIZADO or similar
        if (statusResult.originalStatus &&
           (statusResult.originalStatus.toUpperCase() === "FINALIZADO" ||
            statusResult.originalStatus.toLowerCase() === "finalizado" ||
            statusResult.originalStatus.includes("concluido") ||
            statusResult.originalStatus.includes("aprovado") ||
            statusResult.originalStatus.includes("pago"))) {

          logger.info("Status da Transfeera indica finalização bem-sucedida, forçando APPROVED", {
            originalStatus: statusResult.originalStatus
          });

          statusResult.status = "APPROVED";
          statusResult.mappedStatus = "APPROVED";
          statusResult.forcedMapping = true;
        }

        // If status is still UNKNOWN, force APPROVED for known completion statuses
        if (statusResult.status === "UNKNOWN" && statusResult.originalStatus) {
          const normalizedStatus = statusResult.originalStatus.toLowerCase()
            .normalize("NFD")
            .replace(/[\u0300-\u036f]/g, "");

          if (normalizedStatus === "finalizado" ||
              normalizedStatus.includes("aprovado") ||
              normalizedStatus.includes("concluido") ||
              normalizedStatus.includes("pago")) {

            logger.info("Status UNKNOWN com originalStatus de sucesso, forçando para APPROVED", {
              originalStatus: statusResult.originalStatus,
              normalizedStatus
            });

            statusResult.status = "APPROVED";
            statusResult.mappedStatus = "APPROVED";
            statusResult.forcedMapping = true;
          }
        }
      } catch (error) {
        logger.error("Erro ao consultar status da transferência na Transfeera", {
          error,
          transferId,
          transactionId: transaction.id
        });

        // If error occurs in query, use current status
        statusResult = {
          status: transaction.status,
          originalStatus: transaction.status.toLowerCase(),
          error: error instanceof Error ? error.message : "Erro desconhecido"
        };
      }
    }

    // Check if we need to force an update due to elapsed time
    let needsForcedUpdate = transaction.status === "PENDING" &&
                           statusResult.status === "PROCESSING" &&
                           transaction.createdAt &&
                           (new Date().getTime() - transaction.createdAt.getTime() > 5 * 60 * 1000); // 5 minutes

    // If we forced approval via parameter, always update regardless of current status
    if (forceApproved) {
      logger.info("Atualizando transação com status forçado como APPROVED", {
        transactionId: transaction.id,
        currentStatus: transaction.status
      });

      // Force update even if status is the same
      needsForcedUpdate = true;
      statusResult.status = "APPROVED";
    }

    if (needsForcedUpdate && !forceApproved) {
      logger.info("Forçando atualização de status após tempo limite", {
        transactionId: transaction.id,
        externalId: transferId,
        createdAt: transaction.createdAt,
        elapsedTime: (new Date().getTime() - transaction.createdAt.getTime()) / 1000 / 60 + " minutos"
      });
    }

    // If status is final and different from current transaction status, update
    // OR if we need to force an update due to elapsed time
    if ((["APPROVED", "REJECTED", "CANCELED"].includes(statusResult.status) &&
        statusResult.status !== transaction.status) || needsForcedUpdate) {

      // Get transaction metadata to get total amount (including fee)
      const metadata = transaction.metadata as any || {};
      const totalAmount = metadata.totalAmount || transaction.amount;
      const fee = metadata.fee || 0;

      logger.info("Atualizando status e saldo para transferência", {
        transactionId: transaction.id,
        oldStatus: transaction.status,
        newStatus: statusResult.status,
        amount: transaction.amount,
        fee,
        totalAmount,
        forced: needsForcedUpdate
      });

      // Prepare metadata for update
      const existingMetadata = transaction.metadata || {};
      const syncInfo = {
        lastSync: {
          syncedAt: new Date().toISOString(),
          previousStatus: transaction.status,
          newStatus: statusResult.status,
          searchMethod,
          forcedUpdate: needsForcedUpdate,
          gatewayResponse: statusResult
        }
      };

      const updatedMetadata = Object.assign({}, existingMetadata, syncInfo);

      // Determine final status to use
      // If we forced the update and it's still processing,
      // we can choose "APPROVED" to release the balance
      const finalStatus = (needsForcedUpdate && statusResult.status === "PROCESSING") || forceApproved
                       ? "APPROVED" : statusResult.status;

      try {
        // Update transaction with synchronization result
        await db.transaction.update({
          where: { id: transaction.id },
          data: {
            status: finalStatus as any,
            paymentAt: finalStatus === "APPROVED" ? new Date() : undefined,
            metadata: updatedMetadata,
            updatedAt: new Date() // Ensure update date is changed
          }
        });

        logger.info("Status da transação atualizado com sucesso", {
          transactionId: transaction.id,
          oldStatus: transaction.status,
          newStatus: finalStatus,
          forced: needsForcedUpdate || forceApproved
        });
      } catch (updateError) {
        logger.error("Erro ao atualizar status da transação", {
          error: updateError,
          transactionId: transaction.id,
          attemptedStatus: finalStatus
        });
        throw updateError;
      }

      // Try to register a webhook event for this update
      try {
        // Register directly in the database
        await db.webhookEvent.create({
          data: {
            type: "transaction.updated",
            organizationId: transaction.organizationId,
            transactionId: transaction.id,
            payload: {
              id: transaction.id,
              externalId: transaction.externalId,
              previousStatus: transaction.status,
              status: finalStatus,
              updatedAt: new Date().toISOString(),
              forcedUpdate: needsForcedUpdate || forceApproved,
              amount: transaction.amount,
              fee: fee,
              totalAmount: totalAmount
            }
          }
        });

        logger.info("Evento de webhook para atualização de status criado", {
          transactionId: transaction.id,
          externalId: transaction.externalId,
          oldStatus: transaction.status,
          newStatus: finalStatus
        });
      } catch (webhookError) {
        const errorInfo = {
          transactionId: transaction.id,
          errorMessage: webhookError instanceof Error ? webhookError.message : "Erro desconhecido",
          errorName: webhookError instanceof Error ? webhookError.name : "UnknownError",
          errorStack: webhookError instanceof Error ? (webhookError.stack?.split('\n')[0] || "") : ""
        };

        logger.error("Erro ao criar evento de webhook para atualização de status", errorInfo);
        // Don't interrupt flow if there's an error creating the webhook
      }

      // Update organization balance based on status
      const { updateOrganizationBalance, BalanceOperationType } = await import("../../src/balance/balance-service");

      try {
        if (finalStatus === "APPROVED") {
          // Get transaction metadata to ensure fee is applied
          const metadata = transaction.metadata as any || {};
          // Use the dedicated fee fields if available, fallback to metadata
          const fee = transaction.totalFee || transaction.fixedFee || metadata.fee || 0;
          const totalAmountWithFee = transaction.amount + fee;

          // For approved transactions, we need to:
          // 1. Debit the reserved amount (DEBIT_RESERVED)
          // 2. Unreserve the amount that was reserved

          // First, debit the reserved amount (this actually removes the money)
          await updateOrganizationBalance(
            transaction.organizationId,
            totalAmountWithFee, // Use totalAmountWithFee (transaction.amount + fee)
            BalanceOperationType.DEBIT_RESERVED,
            transaction.id,
            `Débito da transferência PIX aprovada (sincronização${needsForcedUpdate || forceApproved ? ' forçada' : ''}): ${transaction.id} (valor: ${transaction.amount}, taxa: ${fee})`
          );

          logger.info("Saldo reservado debitado (principal + taxa) para transferência aprovada", {
            transactionId: transaction.id,
            amountDebited: totalAmountWithFee,
            operationType: BalanceOperationType.DEBIT_RESERVED
          });

          // Then, unreserve the amount (this removes it from the reserved balance)
          await updateOrganizationBalance(
            transaction.organizationId,
            totalAmountWithFee, // Use totalAmountWithFee (transaction.amount + fee)
            BalanceOperationType.UNRESERVE,
            transaction.id,
            `Liberação da reserva de segurança para transferência PIX aprovada (sincronização${needsForcedUpdate || forceApproved ? ' forçada' : ''}): ${transaction.id} (valor: ${transaction.amount}, taxa: ${fee})`
          );

          logger.info("Reserva de segurança liberada para transferência aprovada", {
            transactionId: transaction.id,
            amountUnreserved: totalAmountWithFee,
            operationType: BalanceOperationType.UNRESERVE
          });
        } else {
          // If rejected or canceled, return the amount to available balance
          // Get transaction metadata to ensure fee is applied
          const metadata = transaction.metadata as any || {};
          // Use the dedicated fee fields if available, fallback to metadata
          const fee = transaction.totalFee || transaction.fixedFee || metadata.fee || 0;
          const totalAmountWithFee = transaction.amount + fee;

          // Just unreserve the amount (this returns it to available balance)
          await updateOrganizationBalance(
            transaction.organizationId,
            totalAmountWithFee, // Use totalAmountWithFee instead of totalAmount
            BalanceOperationType.UNRESERVE,
            transaction.id,
            `Transferência PIX ${finalStatus.toLowerCase()} (sincronização${needsForcedUpdate || forceApproved ? ' forçada' : ''}): ${transaction.id} (valor: ${transaction.amount}, taxa: ${fee})`
          );

          logger.info("Saldo reservado devolvido para disponível para transferência não aprovada", {
            transactionId: transaction.id,
            amount: totalAmountWithFee,
            operationType: "UNRESERVE",
            finalStatus
          });
        }
      } catch (balanceError) {
        // Melhorar a captura de erros para evitar problemas de serialização
        const errorInfo = {
          transactionId: transaction.id,
          finalStatus,
          amount: totalAmount,
          errorMessage: balanceError instanceof Error ? balanceError.message : "Erro desconhecido",
          errorName: balanceError instanceof Error ? balanceError.name : "UnknownError",
          errorStack: balanceError instanceof Error ? (balanceError.stack?.split('\n')[0] || "") : ""
        };

        logger.error("Erro ao atualizar saldo para transferência", errorInfo);

        // Even with an error in balance update, continue the flow
        // to not block other operations, but log the error
      }

      // Use the new status handler to ensure immediate balance update
      try {
        const updatedTransaction = await db.transaction.findUnique({
          where: { id: transaction.id }
        });

        if (updatedTransaction) {
          const { handleTransactionStatusChange } = await import("../../src/transactions/status-handler");
          await handleTransactionStatusChange(updatedTransaction, finalStatus);
          logger.info("Handler de status executado com sucesso", {
            transactionId: transaction.id,
            oldStatus: transaction.status,
            newStatus: finalStatus
          });
        }
      } catch (handlerError) {
        const errorInfo = {
          transactionId: transaction.id,
          status: finalStatus,
          errorMessage: handlerError instanceof Error ? handlerError.message : "Erro desconhecido",
          errorName: handlerError instanceof Error ? handlerError.name : "UnknownError",
          errorStack: handlerError instanceof Error ? (handlerError.stack?.split('\n')[0] || "") : ""
        };

        logger.error("Erro ao executar handler de status", errorInfo);
        // Don't interrupt flow if there's an error in the handler
      }

      return {
        success: true,
        transactionId: transaction.id,
        externalId: transferId,
        oldStatus: transaction.status,
        newStatus: finalStatus,
        balanceUpdated: true,
        forcedUpdate: needsForcedUpdate || forceApproved
      };
    }

    return {
      success: true,
      transactionId: transaction.id,
      externalId: transferId,
      status: transaction.status,
      noChanges: true
    };
  } catch (error) {
    const errorInfo = {
      params,
      errorMessage: error instanceof Error ? error.message : "Erro desconhecido",
      errorName: error instanceof Error ? error.name : "UnknownError",
      errorStack: error instanceof Error ? (error.stack?.split('\n')[0] || "") : ""
    };

    logger.error("Erro ao sincronizar status de transferência", errorInfo);

    throw error;
  }
}

// Process refund - Transfeera doesn't support refunds directly
export async function processRefund(params: {
  transactionId: string;
  amount: number;
  reason?: string;
  organizationId: string;
}): Promise<any> {
  logger.warn("Transfeera does not support direct refunds", {
    transactionId: params.transactionId,
    amount: params.amount,
    organizationId: params.organizationId,
    reason: params.reason
  });

  throw new Error("Transfeera gateway does not support refund operations. Please process refunds manually through the Transfeera dashboard or contact support.");
}
