import { getOrganizationMembership } from "@repo/auth";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { getPaymentProvider } from "@repo/payments";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { HTTPException } from "hono/http-exception";
import { z } from "zod";
import { authMiddleware } from "../../../middleware/auth";
// import { processGatewayPayment } from "./async-processor"; // Comment out or remove if no longer needed for other paths

// Simple in-memory cache for gateway selection (5 minute TTL)
const gatewayCache = new Map<string, { gateway: any; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

// Definir o tipo para as variáveis de contexto corretamente conforme o middleware de autenticação
interface Variables {
  session: {
    id: string;
    userId: string;
    expiresAt: Date;
    token: string;
    createdAt: Date;
    updatedAt: Date;
    activeOrganizationId?: string;
    [key: string]: any;
  };
  user: {
    id: string;
    name?: string;
    email?: string;
    role?: string;
    [key: string]: any;
  };
  organization?: {
    id: string;
    name?: string;
    [key: string]: any;
  };
  apiKey?: {
    id: string;
    organizationId: string;
    [key: string]: any;
  };
}

// Schema para validar a criação de transações
const createTransactionSchema = z.object({
  amount: z.number().positive(),
  customerName: z.string().min(1),
  customerEmail: z.string().email(),
  customerPhone: z.string().optional(),
  customerDocument: z.string().optional(),
  customerDocumentType: z.enum(["cpf", "cnpj"]).optional(),
  description: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  organizationId: z.string(),
  // Gateway fields removed as requested
});

// Importante: Não usamos basePath pois já foi definido no paymentsRouter
export const transactionsRouter = new Hono<{ Variables: Variables }>();

// Create a new transaction (Pix payment)
transactionsRouter.post(
  "/",
  authMiddleware as any,
  validator("json", createTransactionSchema),
  describeRoute({
    tags: ["Transactions"],
    summary: "Create a new transaction (Pix payment)",
    description: "Creates a new Pix payment transaction",
    responses: {
      201: {
        description: "Transaction created successfully",
      },
      400: {
        description: "Invalid request data",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    const timings = {
      start: Date.now(),
      dbLookupComplete: 0,
      gatewaySelectionComplete: 0,
      gatewayCallStart: 0,
      gatewayCallComplete: 0,
      dbInsertComplete: 0
    };
    const abortController = new AbortController();
    const timeoutId = setTimeout(() => {
      logger.warn("Hono request timeout triggered before completion", { path: c.req.path });
      abortController.abort();
    }, 25000); // Increased timeout to 25 seconds for Pluggou + DB

    try {
      const session = c.get("session");
      const {
        amount,
        customerName,
        customerEmail,
        customerPhone,
        customerDocument,
        description,
        metadata: requestMetadata,
        organizationId,
      } = c.req.valid("json");

      const membership = await getOrganizationMembership(session.userId, organizationId);
      if (!membership) {
        clearTimeout(timeoutId);
        throw new HTTPException(403, { message: "You don't have access to this organization" });
      }

      const referenceCode = `tx_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
      const idempotencyKey = `tx_${customerEmail}_${amount}_${Date.now().toString().substring(0, 8)}`; // Consider a more robust idempotency key

      // Ultra-fast duplicate checking - single optimized query
      const twoMinutesAgo = new Date(Date.now() - 2 * 60 * 1000); // Reduced to 2 minutes
      const existingTransaction = await db.transaction.findFirst({
        where: {
          OR: [
            // Exact reference code match (fastest)
            { referenceCode },
            // Recent duplicate by customer + amount (with time limit)
            {
              customerEmail,
              amount,
              organizationId,
              type: "CHARGE",
              createdAt: { gte: twoMinutesAgo }
            }
          ]
        },
        select: {
          id: true,
          referenceCode: true,
          status: true,
          amount: true,
          customerEmail: true,
          customerName: true,
          createdAt: true,
          metadata: true
        },
        orderBy: [
          // Prioritize exact reference matches
          { referenceCode: referenceCode ? 'desc' : 'asc' },
          { createdAt: "desc" }
        ]
      });
      timings.dbLookupComplete = Date.now();

      if (existingTransaction) {
        const existingMetadata = existingTransaction.metadata as Record<string, any> || {};
        if (existingMetadata.pixCode || existingMetadata.pixQrCode) {
          clearTimeout(timeoutId);
          logger.info("Duplicate transaction with PIX data found, returning existing.", { id: existingTransaction.id });
          return c.json({
            id: existingTransaction.id,
            referenceCode: existingTransaction.referenceCode,
            status: existingTransaction.status.toLowerCase(),
            externalId: existingMetadata.txid || existingMetadata.externalId,
            pix: {
              qrCode: {
                emv: existingMetadata.pixCode,
                imagem: existingMetadata.pixQrCode,
              },
              expirationDate: existingMetadata.pixExpiresAt,
              txid: existingMetadata.txid
            },
            paymentInfo: {
              amount: existingTransaction.amount,
              customerEmail: existingTransaction.customerEmail,
              customerName: existingTransaction.customerName,
              createdAt: existingTransaction.createdAt
            },
            message: "PIX charge created successfully",
            _timings: {
              start: timings.start,
              dbLookupComplete: Date.now(),
              gatewaySelectionComplete: Date.now(),
              gatewayCallStart: Date.now(),
              gatewayCallComplete: Date.now(),
              dbInsertComplete: Date.now()
            }
          }, 200); // 200 OK for existing resource
        }
        // If duplicate exists but no PIX data, proceed to generate PIX for it
        logger.info("Duplicate transaction found without PIX data, proceeding to generate PIX for it.", { id: existingTransaction.id });
      }

      // Ultra-fast gateway selection with cache
      let gateway;
      try {
        const cacheKey = "FLOW2PAY_GATEWAY";
        const cached = gatewayCache.get(cacheKey);

        // Check cache first
        if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
          gateway = cached.gateway;
        } else {
          // Direct lookup for PLUGGOU_PIX gateway (fastest approach)
          gateway = await db.paymentGateway.findFirst({
            where: {
              type: "FLOW2PAY",
              isActive: true,
              canReceive: true
            },
            select: {
              id: true,
              type: true,
              name: true
            }
          });

          // Fallback to any active receiving gateway if PLUGGOU_PIX not found
          if (!gateway) {
            gateway = await db.paymentGateway.findFirst({
              where: {
                isActive: true,
                canReceive: true
              },
              select: {
                id: true,
                type: true,
                name: true
              },
              orderBy: { priority: 'asc' }
            });
          }

          // Cache the result
          if (gateway) {
            gatewayCache.set(cacheKey, { gateway, timestamp: Date.now() });
          }
        }

        if (!gateway) {
          throw new Error("No active payment gateway configured for receiving payments");
        }
      } catch (error) {
        clearTimeout(timeoutId);
        logger.error("Error finding payment gateway", {
          error,
          timeTaken: Date.now() - timings.start
        });
        throw new HTTPException(500, { message: "Failed to find active payment gateway" });
      }
      timings.gatewaySelectionComplete = Date.now();

      // Direct provider import for PLUGGOU_PIX (fastest approach)
      let provider;
      if (gateway.type === "FLOW2PAY") {
        const { createPixPayment } = await import("@repo/payments/provider/flow2pay");
        provider = { createPixPayment };
      } else {
        provider = await getPaymentProvider(gateway.type as any);
      }

      if (!provider || !provider.createPixPayment) {
        clearTimeout(timeoutId);
        throw new HTTPException(500, { message: `Payment provider for ${gateway.type} not found or does not support PIX payments` });
      }

      logger.info("Attempting synchronous PIX creation with provider", { gatewayType: gateway.type, organizationId });
      timings.gatewayCallStart = Date.now();

      // Prepare parameters for the provider's createPixPayment method
      // The Pluggou provider handles its own DB interaction for initial creation/update
      const paymentResult = await provider.createPixPayment({
        amount,
        customerName,
        customerEmail,
        customerPhone,
        customerDocument,
        description: description || `Cobrança PIX para ${customerName}`,
        organizationId,
        externalCode: existingTransaction?.referenceCode || referenceCode,
        idempotencyKey: idempotencyKey,
        metadata: {
          ...(requestMetadata || {}),
          source: "api-synchronous",
          gatewayId: gateway.id,
          gatewayName: gateway.name,
        },
      });

      timings.gatewayCallComplete = Date.now();

      if (!paymentResult || !paymentResult.success) {
        clearTimeout(timeoutId);
        logger.error("Payment provider failed to create PIX payment", { error: paymentResult, organizationId, gatewayType: gateway.type });
        // Attempt to update transaction with failure details if a record was made by provider
        if (paymentResult?.transactionId) {
           await db.transaction.updateMany({ // Use updateMany if ID is not unique or certain
            where: { id: paymentResult.transactionId, organizationId },
            data: {
              status: "REJECTED",
              metadata: {
                ...( (await db.transaction.findUnique({ where: {id: paymentResult.transactionId}}))?.metadata as Record<string, any> || {}),
                processingError: true,
                processingErrorMessage: "Provider failed to create PIX payment synchronously.",
              }
            }
          });
        }
        throw new HTTPException(500, { message: "Failed to create PIX payment with provider" });
      }

      // Create or update transaction in database (fast operation)
      let dbTransaction;
      if (existingTransaction) {
        // Update existing transaction
        dbTransaction = await db.transaction.update({
          where: { id: existingTransaction.id },
          data: {
            externalId: paymentResult.externalId,
            metadata: paymentResult.metadata
          }
        });
      } else {
        // Create new transaction
        dbTransaction = await db.transaction.create({
          data: {
            externalId: paymentResult.externalId,
            referenceCode: referenceCode,
            customerName,
            customerEmail,
            customerPhone: customerPhone || "",
            customerDocument: customerDocument || "",
            amount,
            status: "PENDING",
            type: "CHARGE",
            description: description || "Pagamento via Pix",
            metadata: paymentResult.metadata,
            organizationId,
            gatewayId: gateway.id,
          }
        });
      }

      // Update the payment result with the actual transaction ID
      paymentResult.transactionId = dbTransaction.id;
      timings.dbInsertComplete = Date.now();

      logger.info("Synchronous PIX creation successful", {
        transactionId: paymentResult.transactionId,
        externalId: paymentResult.externalId,
        organizationId
      });

      // Trigger webhook events asynchronously (don't wait)
      if (!existingTransaction) {
        setImmediate(async () => {
          try {
            const { triggerTransactionEvents } = await import("@repo/payments/src/webhooks/events");
            await triggerTransactionEvents(dbTransaction);
          } catch (webhookError) {
            logger.error("Failed to trigger webhook events", {
              error: webhookError,
              transactionId: dbTransaction.id
            });
          }
        });
      }

      clearTimeout(timeoutId);

      // Optimized response structure - clean and simplified
      return c.json({
        id: paymentResult.transactionId,
        referenceCode: existingTransaction?.referenceCode || referenceCode,
        status: paymentResult.status?.toLowerCase() || "pending",
        externalId: paymentResult.externalId,
        pix: {
          qrCode: {
            emv: paymentResult.qrCode || paymentResult.pix?.payload,
            imagem: paymentResult.qrCodeImage || paymentResult.pix?.encodedImage,
          },
          expirationDate: paymentResult.expiration || paymentResult.pix?.expirationDate,
          txid: paymentResult.txid || paymentResult.externalId
        },
        paymentInfo: {
          amount: amount,
          customerEmail: customerEmail,
          customerName: customerName,
          createdAt: existingTransaction?.createdAt || new Date().toISOString(),
        },
        message: "PIX charge created successfully",
        _timings: timings,
      }, 201);

    } catch (error: any) {
      clearTimeout(timeoutId);
      logger.error("Error in POST /api/payments/transactions", {
        error: error.message,
        stack: error.stack,
        organizationId: c.req.valid("json")?.organizationId,
        timings,
      });

      if (error instanceof HTTPException) {
        throw error;
      }
      throw new HTTPException(500, { message: error.message || "Internal server error creating transaction" });
    }
  }
);

// List transactions
transactionsRouter.get(
  "/",
  authMiddleware as any, // Cast to any to bypass type issues temporarily
  validator("query", z.object({
    organizationId: z.string(),
    status: z.enum(["PENDING", "APPROVED", "REJECTED", "CANCELED", "PROCESSING", "REFUNDED", "BLOCKED"]).optional(),
    type: z.enum(["CHARGE", "SEND", "RECEIVE"]).optional(),
    startDate: z.string().optional(),
    endDate: z.string().optional(),
    page: z.string().transform(Number).default("1"),
    limit: z.string().transform(Number).default("10"),
  })),
  describeRoute({
    tags: ["Transactions"],
    summary: "List transactions",
    description: "Lists transactions with optional filters",
    responses: {
      200: {
        description: "Transactions retrieved successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const {
        organizationId,
        status,
        type,
        startDate,
        endDate,
        page,
        limit
      } = c.req.valid("query");

      // Check if using API key authentication
      const apiKey = c.get("apiKey");
      const organization = c.get("organization");

      // If using API key, verify the organization matches
      if (apiKey && organization) {
        if (organization.id !== organizationId) {
          throw new HTTPException(403, { message: "API key does not have access to this organization" });
        }
      } else {
        // Otherwise verify normal membership
        const membership = await getOrganizationMembership(session.userId, organizationId);
        if (!membership) {
          throw new HTTPException(403, { message: "You don't have access to this organization" });
        }
      }

      // Build the query
      const where: any = {
        organizationId,
      };

      if (status) {
        where.status = status;
      }

      if (type) {
        where.type = type;
      }

      if (startDate || endDate) {
        where.createdAt = {};

        if (startDate) {
          where.createdAt.gte = new Date(startDate);
        }

        if (endDate) {
          where.createdAt.lte = new Date(endDate);
        }
      }

      // Get total count
      const total = await db.transaction.count({ where });

      // Get transactions
      const transactions = await db.transaction.findMany({
        where,
        orderBy: {
          createdAt: "desc",
        },
        skip: (page - 1) * limit,
        take: limit,
      });

      return c.json({
        data: transactions.map(tx => {
          return {
            id: tx.id,
            externalId: tx.externalId,
            referenceCode: tx.referenceCode,
            customerName: tx.customerName,
            customerEmail: tx.customerEmail,
            amount: tx.amount,
            status: tx.status,
            type: tx.type,
            createdAt: tx.createdAt,
            paymentAt: tx.paymentAt,
          };
        }),
        pagination: {
          total,
          page,
          limit,
          pages: Math.ceil(total / limit),
        },
      });
    } catch (error) {
      logger.error("Error listing transactions", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to list transactions" });
    }
  }
);

// Get transaction by ID
transactionsRouter.get(
  "/:id",
  authMiddleware as any, // Cast to any to bypass type issues temporarily
  validator("param", z.object({
    id: z.string(),
  })),
  describeRoute({
    tags: ["Transactions"],
    summary: "Get transaction by ID",
    description: "Retrieves a transaction by its ID",
    responses: {
      200: {
        description: "Transaction retrieved successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      404: {
        description: "Transaction not found",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const { id } = c.req.valid("param");

      const transaction = await db.transaction.findUnique({
        where: { id },
        include: {
          gateway: true,
          refunds: true,
          blocks: true,
        },
      });

      if (!transaction) {
        throw new HTTPException(404, { message: "Transaction not found" });
      }

      // Check if using API key authentication
      const apiKey = c.get("apiKey");
      const organization = c.get("organization");

      // If using API key, verify the organization matches
      if (apiKey && organization) {
        if (organization.id !== transaction.organizationId) {
          throw new HTTPException(403, { message: "API key does not have access to this transaction" });
        }
      } else {
        // Otherwise verify normal membership
        const membership = await getOrganizationMembership(session.userId, transaction.organizationId);
        if (!membership) {
          throw new HTTPException(403, { message: "You don't have access to this transaction" });
        }
      }

      // If the transaction is pending, check its status with the gateway
      if (transaction.status === "PENDING" && transaction.externalId) {
        try {
          // Get the payment provider
          const provider = await getPaymentProvider(transaction.organizationId);

          const statusResult = await provider.getTransactionStatus({
            transactionId: transaction.externalId,
            organizationId: transaction.organizationId,
          });

          // Map status to our internal status
          let mappedStatus = transaction.status;

          if (statusResult.mappedStatus) {
            // If the provider returns a mapped status, use it
            mappedStatus = statusResult.mappedStatus;
          } else if (statusResult.status) {
            // Otherwise, try to map it ourselves
            const statusMap: Record<string, string> = {
              "pending": "PENDING",
              "approved": "APPROVED",
              "confirmed": "APPROVED",
              "received": "APPROVED",
              "rejected": "REJECTED",
              "canceled": "CANCELED",
              "processing": "PROCESSING",
              "refunded": "REFUNDED",
            };

            mappedStatus = statusMap[statusResult.status.toLowerCase()] || statusResult.status.toUpperCase();
          }

          // Update the transaction if the status has changed
          if (mappedStatus !== transaction.status) {
            const previousStatus = transaction.status;

            const updatedTransaction = await db.transaction.update({
              where: { id },
              data: {
                status: mappedStatus as any,
                paymentAt: statusResult.status === "approved" ? new Date() : transaction.paymentAt,
              },
            });

            // Trigger webhook events for the status change
            try {
              const { triggerTransactionEvents } = await import("@repo/payments/src/webhooks/events");
              await triggerTransactionEvents(updatedTransaction, previousStatus);
              logger.info("Triggered webhook events for status update during GET", {
                transactionId: id,
                previousStatus,
                newStatus: mappedStatus
              });
            } catch (webhookError) {
              logger.error("Failed to trigger webhook events for status update during GET", {
                error: webhookError,
                transactionId: id,
                previousStatus,
                newStatus: mappedStatus
              });
              // Don't fail the GET request if webhook triggering fails
            }

            // Refresh the transaction data
            transaction.status = mappedStatus as any;
            if (statusResult.status === "approved") {
              transaction.paymentAt = new Date();
            }
          }
        } catch (error) {
          logger.error("Error checking transaction status", { error, transactionId: id });
          // Continue with the current transaction data
        }
      }

      const metadata = transaction.metadata as Record<string, any> || {};

      // Log more detailed info about transaction to debug issues
      logger.info("Transaction details fetched", {
        transactionId: id,
        status: transaction.status,
        hasMetadata: !!transaction.metadata,
        metadataKeys: metadata ? Object.keys(metadata) : [],
        gatewayInfo: transaction.gateway ?
          { id: transaction.gateway.id, type: transaction.gateway.type } :
          'No gateway info'
      });

      // Get endToEndId from the dedicated column, with fallback to metadata for backward compatibility
      const endToEndId = transaction.endToEndId ||
                         metadata?.endToEndId ||
                         metadata?.pixEndToEndId ||
                         metadata?.transferDetails?.endToEndId ||
                         metadata?.paymentDetails?.endToEndId;

      return c.json({
        id: transaction.id,
        externalId: transaction.externalId,
        referenceCode: transaction.referenceCode,
        customerName: transaction.customerName,
        customerEmail: transaction.customerEmail,
        customerPhone: transaction.customerPhone,
        customerDocument: transaction.customerDocument,
        customerDocumentType: transaction.customerDocumentType,
        amount: transaction.amount,
        status: transaction.status,
        type: transaction.type,
        description: transaction.description,
        createdAt: transaction.createdAt,
        paymentAt: transaction.paymentAt,
        updatedAt: transaction.updatedAt,
        // Include any PIX details from metadata
        pixCode: metadata?.pixCode || metadata?.pixCopiaECola,
        pixQrCode: metadata?.pixQrCode || metadata?.qrCodeBase64 || metadata?.qrCodeUrl,
        pixExpiresAt: metadata?.pixExpiresAt || metadata?.expirationDate,
        // Include PIX specific identifiers
        endToEndId: endToEndId,
        pixEndToEndId: endToEndId,
        // Gateway information removed as requested
        refunds: transaction.refunds.map(refund => ({
          id: refund.id,
          amount: refund.amount,
          status: refund.status,
          reason: refund.reason,
          createdAt: refund.createdAt,
          processedAt: refund.processedAt,
        })),
        blocks: transaction.blocks.map(block => ({
          id: block.id,
          reason: block.reason,
          status: block.status,
          createdAt: block.createdAt,
          releasedAt: block.releasedAt,
        })),
      });
    } catch (error) {
      logger.error("Error getting transaction", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to get transaction" });
    }
  }
);

// Process a refund
// transactionsRouter.post(
//   "/:id/refund",
//   authMiddleware,
//   validator("param", z.object({
//     id: z.string(),
//   })),
//   validator("json", z.object({
//     amount: z.number().positive().optional(),
//     reason: z.string().optional(),
//   })),
//   describeRoute({
//     tags: ["Transactions"],
//     summary: "Process a refund",
//     description: "Processes a refund for a transaction",
//     responses: {
//       200: {
//         description: "Refund processed successfully",
//       },
//       400: {
//         description: "Invalid request data",
//       },
//       401: {
//         description: "Unauthorized",
//       },
//       403: {
//         description: "Forbidden",
//       },
//       404: {
//         description: "Transaction not found",
//       },
//       500: {
//         description: "Internal server error",
//       },
//     },
//   }),
//   resolver(async (c) => {
//     try {
//       const session = c.get("session");
//       const { id } = c.req.valid("param");
//       const { amount, reason } = c.req.valid("json");

//       const transaction = await db.transaction.findUnique({
//         where: { id },
//       });

//       if (!transaction) {
//         throw new HTTPException(404, { message: "Transaction not found" });
//       }

//       // Verify organization membership
//       const membership = await getOrganizationMembership(session.userId, transaction.organizationId);
//       if (!membership) {
//         throw new HTTPException(403, { message: "You don't have access to this transaction" });
//       }

//       // Check if the transaction can be refunded
//       if (transaction.status !== "APPROVED") {
//         throw new HTTPException(400, { message: "Only approved transactions can be refunded" });
//       }

//       // Use the full amount if not specified
//       const refundAmount = amount || transaction.amount;

//       // Create the refund in the database
//       const refund = await db.refund.create({
//         data: {
//           amount: refundAmount,
//           reason,
//           status: "PENDING",
//           transactionId: transaction.id,
//         },
//       });

//       // Process the refund with the appropriate gateway
//       if (transaction.externalId) {
//         try {
//           // Get the payment provider
//           const provider = await getPaymentProvider(transaction.organizationId);

//           const refundResult = await provider.processRefund({
//             transactionId: transaction.externalId,
//             amount: refundAmount,
//             reason,
//             organizationId: transaction.organizationId,
//           });

//           // Update the refund with the external ID
//           await db.refund.update({
//             where: { id: refund.id },
//             data: {
//               externalId: refundResult.id,
//               status: "PROCESSING",
//             },
//           });

//           // Update the transaction status
//           await db.transaction.update({
//             where: { id: transaction.id },
//             data: {
//               status: "REFUNDED",
//             },
//           });

//           return c.json({
//             id: refund.id,
//             amount: refund.amount,
//             status: "PROCESSING",
//             transactionId: transaction.id,
//           });
//         } catch (error) {
//           logger.error("Error processing refund with gateway", { error, transactionId: id });

//           // Update the refund status to failed
//           await db.refund.update({
//             where: { id: refund.id },
//             data: {
//               status: "REJECTED",
//             },
//           });

//           throw new HTTPException(500, { message: "Failed to process refund with payment gateway" });
//         }
//       } else {
//         throw new HTTPException(400, { message: "Transaction has no external ID" });
//       }
//     } catch (error) {
//       logger.error("Error processing refund", { error });

//       if (error instanceof HTTPException) {
//         throw error;
//       }

//       throw new HTTPException(500, { message: "Failed to process refund" });
//     }
//   })
// );

// Create a cautionary block
transactionsRouter.post(
  "/:id/block",
  authMiddleware as any,
  validator("param", z.object({
    id: z.string(),
  })),
  validator("json", z.object({
    reason: z.string(),
  })),
  describeRoute({
    tags: ["Transactions"],
    summary: "Create a cautionary block",
    description: "Creates a cautionary block for a transaction",
    responses: {
      200: {
        description: "Block created successfully",
      },
      400: {
        description: "Invalid request data",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      404: {
        description: "Transaction not found",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const { id } = c.req.valid("param");
      const { reason } = c.req.valid("json");

      const transaction = await db.transaction.findUnique({
        where: { id },
      });

      if (!transaction) {
        throw new HTTPException(404, { message: "Transaction not found" });
      }

      // Verify organization membership
      const membership = await getOrganizationMembership(session.userId, transaction.organizationId);
      if (!membership) {
        throw new HTTPException(403, { message: "You don't have access to this transaction" });
      }

      // Check if the transaction already has an active block
      const existingBlock = await db.cautionaryBlock.findFirst({
        where: {
          transactionId: transaction.id,
          status: "ACTIVE",
        },
      });

      if (existingBlock) {
        throw new HTTPException(400, { message: "Transaction already has an active block" });
      }

      // Create the block
      const block = await db.cautionaryBlock.create({
        data: {
          reason,
          status: "ACTIVE",
          transactionId: transaction.id,
        },
      });

      // Update the transaction status
      await db.transaction.update({
        where: { id: transaction.id },
        data: {
          status: "BLOCKED",
        },
      });

      return c.json({
        id: block.id,
        reason: block.reason,
        status: block.status,
        transactionId: transaction.id,
        createdAt: block.createdAt,
      });
    } catch (error) {
      logger.error("Error creating cautionary block", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to create cautionary block" });
    }
  }
);

// Release a cautionary block
transactionsRouter.post(
  "/:id/unblock",
  authMiddleware as any,
  validator("param", z.object({
    id: z.string(),
  })),
  describeRoute({
    tags: ["Transactions"],
    summary: "Release a cautionary block",
    description: "Releases a cautionary block for a transaction",
    responses: {
      200: {
        description: "Block released successfully",
      },
      400: {
        description: "Invalid request data",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      404: {
        description: "Transaction or block not found",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const { id } = c.req.valid("param");

      const transaction = await db.transaction.findUnique({
        where: { id },
      });

      if (!transaction) {
        throw new HTTPException(404, { message: "Transaction not found" });
      }

      // Verify organization membership
      const membership = await getOrganizationMembership(session.userId, transaction.organizationId);
      if (!membership) {
        throw new HTTPException(403, { message: "You don't have access to this transaction" });
      }

      // Find the active block
      const block = await db.cautionaryBlock.findFirst({
        where: {
          transactionId: transaction.id,
          status: "ACTIVE",
        },
      });

      if (!block) {
        throw new HTTPException(404, { message: "No active block found for this transaction" });
      }

      // Release the block
      await db.cautionaryBlock.update({
        where: { id: block.id },
        data: {
          status: "RELEASED",
          releasedAt: new Date(),
        },
      });

      // Restore the transaction's previous status
      await db.transaction.update({
        where: { id: transaction.id },
        data: {
          status: "APPROVED", // Assuming it was approved before being blocked
        },
      });

      return c.json({
        id: block.id,
        status: "RELEASED",
        transactionId: transaction.id,
        releasedAt: new Date(),
      });
    } catch (error) {
      logger.error("Error releasing cautionary block", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to release cautionary block" });
    }
  }
);

// Export transactions as CSV
transactionsRouter.get(
  "/export",
  authMiddleware as any,
  validator("query", z.object({
    organizationId: z.string(),
    status: z.enum(["PENDING", "APPROVED", "REJECTED", "CANCELED", "PROCESSING", "REFUNDED", "BLOCKED"]).optional(),
    type: z.enum(["CHARGE", "SEND", "RECEIVE"]).optional(),
    startDate: z.string().optional(),
    endDate: z.string().optional(),
  })),
  describeRoute({
    tags: ["Transactions"],
    summary: "Export transactions as CSV",
    description: "Exports transactions as CSV with optional filters",
    responses: {
      200: {
        description: "CSV file",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const {
        organizationId,
        status,
        type,
        startDate,
        endDate
      } = c.req.valid("query");

      // Verify organization membership
      const membership = await getOrganizationMembership(session.userId, organizationId);
      if (!membership) {
        throw new HTTPException(403, { message: "You don't have access to this organization" });
      }

      // Build the query
      const where: any = {
        organizationId,
      };

      if (status) {
        where.status = status;
      }

      if (type) {
        where.type = type;
      }

      if (startDate || endDate) {
        where.createdAt = {};

        if (startDate) {
          where.createdAt.gte = new Date(startDate);
        }

        if (endDate) {
          where.createdAt.lte = new Date(endDate);
        }
      }

      // Get transactions
      const transactions = await db.transaction.findMany({
        where,
        orderBy: {
          createdAt: "desc",
        },
        include: {
          gateway: {
            select: {
              name: true,
            },
          },
        },
      });

      // Create CSV content
      let csv = "ID,Reference,Customer,Email,Amount,Status,Type,Created At,Payment At,Gateway\n";

      transactions.forEach(tx => {
        const row = [
          tx.id,
          tx.referenceCode || tx.externalId || "",
          tx.customerName,
          tx.customerEmail,
          tx.amount.toFixed(2),
          tx.status,
          tx.type,
          tx.createdAt.toISOString(),
          tx.paymentAt ? tx.paymentAt.toISOString() : "",
          tx.gateway ? tx.gateway.name : "",
        ].map(field => `"${String(field).replace(/"/g, '""')}"`).join(",");

        csv += row + "\n";
      });

      // Set headers for CSV download
      c.header("Content-Type", "text/csv");
      c.header("Content-Disposition", `attachment; filename="transactions-${new Date().toISOString().split("T")[0]}.csv"`);

      return c.body(csv);
    } catch (error) {
      logger.error("Error exporting transactions", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to export transactions" });
    }
  }
);

// Sync transaction status with gateway
transactionsRouter.post(
  "/:id/sync",
  authMiddleware as any,
  validator("param", z.object({
    id: z.string(),
  })),
  describeRoute({
    tags: ["Transactions"],
    summary: "Sync transaction status with gateway",
    description: "Manually syncs a transaction's status with the payment gateway",
    responses: {
      200: {
        description: "Transaction status synced successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      404: {
        description: "Transaction not found",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const { id } = c.req.valid("param");

      // Find the transaction
      const transaction = await db.transaction.findUnique({
        where: { id },
        include: {
          gateway: true,
        },
      });

      if (!transaction) {
        throw new HTTPException(404, { message: "Transaction not found" });
      }

      // Check if using API key authentication
      const apiKey = c.get("apiKey");
      const organization = c.get("organization");

      // If using API key, verify the organization matches
      if (apiKey && organization) {
        if (organization.id !== transaction.organizationId) {
          throw new HTTPException(403, { message: "API key does not have access to this transaction" });
        }
      } else {
        // Otherwise verify normal membership
        const membership = await getOrganizationMembership(session.userId, transaction.organizationId);
        if (!membership) {
          throw new HTTPException(403, { message: "You don't have access to this transaction" });
        }
      }

      // Get the payment provider that processed this transaction
      const provider = await getPaymentProvider(transaction.organizationId, {
        forceType: transaction.gateway?.type,
        action: 'status'
      });

      if (!provider) {
        throw new HTTPException(400, { message: "Payment provider not found" });
      }

      // Check status with the gateway, passing transaction type for better handling
      const statusResult = await provider.getTransactionStatus({
        transactionId: transaction.externalId || transaction.id,
        organizationId: transaction.organizationId,
        transactionType: transaction.type as 'CHARGE' | 'SEND',
      });

      // Map the status
      let mappedStatus = transaction.status;
      if (statusResult.mappedStatus) {
        // If the provider returns a mapped status, use it directly
        mappedStatus = statusResult.mappedStatus;
      } else if (statusResult.status) {
        // Fallback mapping
        const statusMap: Record<string, string> = {
          "PENDING": "PENDING",
          "APPROVED": "APPROVED",
          "CONFIRMED": "APPROVED",
          "RECEIVED": "APPROVED",
          "REJECTED": "REJECTED",
          "CANCELED": "CANCELED",
          "PROCESSING": "PROCESSING",
          "REFUNDED": "REFUNDED",
        };
        mappedStatus = (statusMap[statusResult.status.toUpperCase()] || transaction.status) as any;
      }

      // Update the transaction if the status has changed
      let updatedTransaction = transaction;
      if (mappedStatus !== transaction.status) {
        const isCompleted = mappedStatus === "APPROVED" ||
                          mappedStatus === "REFUNDED";

        updatedTransaction = await db.transaction.update({
          where: { id },
          data: {
            status: mappedStatus,
            paymentAt: isCompleted ? new Date() : transaction.paymentAt,
          },
          include: {
            gateway: true,
          },
        });
      }

      return c.json({
        success: true,
        transaction: {
          id: updatedTransaction.id,
          externalId: updatedTransaction.externalId,
          status: updatedTransaction.status,
          amount: updatedTransaction.amount,
          customerName: updatedTransaction.customerName,
          customerEmail: updatedTransaction.customerEmail,
          type: updatedTransaction.type,
          date: updatedTransaction.createdAt.toISOString(),
          paymentDate: updatedTransaction.paymentAt?.toISOString(),
          updatedAt: updatedTransaction.updatedAt.toISOString(),
          gateway: updatedTransaction.gateway ? {
            name: updatedTransaction.gateway.name,
            type: updatedTransaction.gateway.type,
          } : null,
        },
        previousStatus: transaction.status,
        newStatus: updatedTransaction.status,
        message: mappedStatus !== transaction.status
          ? "Transaction status updated successfully"
          : "Transaction status is already up to date",
      });
    } catch (error) {
      logger.error("Error syncing transaction status", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to sync transaction status" });
    }
  }
);

// Get transactions summary
transactionsRouter.get(
  "/summary",
  authMiddleware as any,
  validator("query", z.object({
    organizationId: z.string(),
  })),
  describeRoute({
    tags: ["Transactions"],
    summary: "Get transactions summary",
    description: "Get summary statistics for transactions",
    responses: {
      200: {
        description: "Transactions summary retrieved successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const { organizationId } = c.req.valid("query");

      // Verify organization membership
      const membership = await getOrganizationMembership(session.userId, organizationId);
      if (!membership) {
        throw new HTTPException(403, { message: "You don't have access to this organization" });
      }

      // Get total transactions count
      const totalTransactions = await db.transaction.count({
        where: { organizationId }
      });

      // Get approved transactions count
      const approvedTransactions = await db.transaction.count({
        where: {
          organizationId,
          status: "APPROVED"
        }
      });

      // Get pending transactions count
      const pendingTransactions = await db.transaction.count({
        where: {
          organizationId,
          status: "PENDING"
        }
      });

      // Get total financial volume
      const transactions = await db.transaction.findMany({
        where: { organizationId },
        select: { amount: true }
      });

      const financialVolume = transactions.reduce((sum, tx) => sum + tx.amount, 0);
      const averageTicket = transactions.length > 0 ? financialVolume / transactions.length : 0;

      // Get 30 day old counts for growth calculation
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const oldTotalTransactions = await db.transaction.count({
        where: {
          organizationId,
          createdAt: { lt: thirtyDaysAgo }
        }
      });

      const oldApprovedTransactions = await db.transaction.count({
        where: {
          organizationId,
          status: "APPROVED",
          createdAt: { lt: thirtyDaysAgo }
        }
      });

      const oldPendingTransactions = await db.transaction.count({
        where: {
          organizationId,
          status: "PENDING",
          createdAt: { lt: thirtyDaysAgo }
        }
      });

      const oldTransactions = await db.transaction.findMany({
        where: {
          organizationId,
          createdAt: { lt: thirtyDaysAgo }
        },
        select: { amount: true }
      });

      const oldFinancialVolume = oldTransactions.reduce((sum, tx) => sum + tx.amount, 0);

      // Calculate growth percentages
      const calculateGrowth = (current: number, previous: number) => {
        if (previous === 0) return current > 0 ? 100 : 0;
        return ((current - previous) / previous) * 100;
      };

      const totalTransactionsGrowth = calculateGrowth(totalTransactions, oldTotalTransactions);
      const approvedTransactionsGrowth = calculateGrowth(approvedTransactions, oldApprovedTransactions);
      const pendingTransactionsGrowth = calculateGrowth(pendingTransactions, oldPendingTransactions);
      const financialVolumeGrowth = calculateGrowth(financialVolume, oldFinancialVolume);

      // Calculate approval rate
      const approvalRate = totalTransactions > 0 ? (approvedTransactions / totalTransactions) * 100 : 0;

      return c.json({
        totalTransactions: {
          count: totalTransactions,
          growth: totalTransactionsGrowth,
        },
        approvedTransactions: {
          count: approvedTransactions,
          growth: approvedTransactionsGrowth,
          approvalRate,
        },
        pendingTransactions: {
          count: pendingTransactions,
          growth: pendingTransactionsGrowth,
        },
        financialVolume: {
          amount: financialVolume,
          growth: financialVolumeGrowth,
          averageTicket,
        },
      });
    } catch (error) {
      logger.error("Error getting transactions summary", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to get transactions summary" });
    }
  }
);
