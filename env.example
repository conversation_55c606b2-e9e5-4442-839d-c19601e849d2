# =============================================================================
# GATEWAY CONFIGURATION
# =============================================================================

# Default payment gateway (for maximum performance)
# Options: PLUGGOU_PIX, FLOW2PAY, TRANSFEERA, PIXIUM, REFLOWPAY, PRIMEPAG, MEDIUSPAG
DEFAULT_PAYMENT_GATEWAY=PLUGGOU_PIX

# =============================================================================
# PLUGGOU PIX CREDENTIALS
# =============================================================================
PLUGGOU_PIX_API_KEY=your_pluggou_api_key
PLUGGOU_PIX_API_URL=https://apipix.cloud.pluggou.io
PLUGGOU_PIX_ENVIRONMENT=sandbox
PLUGGOU_PIX_WEBHOOK_SECRET=your_pluggou_webhook_secret

# =============================================================================
# FLOW2PAY CREDENTIALS
# =============================================================================
FLOW2PAY_CLIENT_ID=your_flow2pay_client_id
FLOW2PAY_CLIENT_SECRET=your_flow2pay_client_secret
FLOW2PAY_EVENT_TOKEN=your_flow2pay_event_token
FLOW2PAY_API_URL=https://pixv2.flow2pay.com.br
FLOW2PAY_ENVIRONMENT=sandbox
FLOW2PAY_TIMEOUT=15000
FLOW2PAY_RETRIES=2

# =============================================================================
# TRANSFEERA CREDENTIALS
# =============================================================================
TRANSFEERA_CLIENT_ID=your_transfeera_client_id
TRANSFEERA_CLIENT_SECRET=your_transfeera_client_secret
TRANSFEERA_ENVIRONMENT=sandbox
TRANSFEERA_WEBHOOK_SECRET=your_transfeera_webhook_secret

# =============================================================================
# PIXIUM CREDENTIALS
# =============================================================================
PIXIUM_API_KEY=your_pixium_api_key
PIXIUM_API_SECRET=your_pixium_api_secret
PIXIUM_ENVIRONMENT=sandbox
PIXIUM_WEBHOOK_SECRET=your_pixium_webhook_secret

# =============================================================================
# REFLOWPAY CREDENTIALS
# =============================================================================
REFLOWPAY_API_KEY=your_reflowpay_api_key
REFLOWPAY_API_SECRET=your_reflowpay_api_secret
REFLOWPAY_ENVIRONMENT=sandbox
REFLOWPAY_WEBHOOK_SECRET=your_reflowpay_webhook_secret

# =============================================================================
# PRIMEPAG CREDENTIALS
# =============================================================================
PRIMEPAG_API_KEY=your_primepag_api_key
PRIMEPAG_API_SECRET=your_primepag_api_secret
PRIMEPAG_ENVIRONMENT=sandbox
PRIMEPAG_WEBHOOK_SECRET=your_primepag_webhook_secret

# =============================================================================
# MEDIUSPAG CREDENTIALS
# =============================================================================
MEDIUSPAG_API_KEY=your_mediuspag_api_key
MEDIUSPAG_API_SECRET=your_mediuspag_api_secret
MEDIUSPAG_ENVIRONMENT=sandbox
MEDIUSPAG_WEBHOOK_SECRET=your_mediuspag_webhook_secret

# =============================================================================
# MOCKSIM CREDENTIALS (for testing)
# =============================================================================
MOCKSIM_API_KEY=mock_key
MOCKSIM_API_SECRET=mock_secret

# =============================================================================
# PERFORMANCE NOTES
# =============================================================================
# When DEFAULT_PAYMENT_GATEWAY is set and corresponding credentials are
# provided via environment variables, the system will:
#
# 1. Skip database queries for gateway selection
# 2. Skip database queries for credential retrieval
# 3. Use direct provider instantiation for maximum speed
# 4. Cache credentials for 5 minutes to optimize repeated calls
#
# This results in significantly faster transaction processing!
