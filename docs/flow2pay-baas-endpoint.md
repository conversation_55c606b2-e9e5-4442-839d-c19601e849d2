# Flow2Pay BaaS Webhook Endpoint

## Visão Geral

O endpoint `/baas` é responsável por receber webhooks da Flow2Pay para processar eventos relacionados a transações PIX. Este endpoint substitui a implementação anterior do projeto PixAPI (Encore.dev) e agora está integrado ao sistema Next.js.

## URL do Endpoint

```
POST /baas
```

## Autenticação

O endpoint valida webhooks através do campo `token` no payload. O token deve corresponder à variável de ambiente `FLOW2PAY_EVENT_TOKEN`.

```javascript
// Validação do token
if (payload.token) {
  const expectedToken = process.env.FLOW2PAY_EVENT_TOKEN;
  if (expectedToken && payload.token !== expectedToken) {
    return NextResponse.json({ error: "Invalid authentication token" }, { status: 401 });
  }
}
```

## Eventos Suportados

### 1. PixIn - Pagamentos Recebidos (QR Code)

Evento disparado quando um pagamento PIX é recebido via QR Code.

**Estrutura do Payload:**
```json
{
  "evento": "PixIn",
  "token": "event_token",
  "txid": "transaction_id",
  "endToEndId": "E12345678...",
  "codigoTransacao": "internal_code",
  "status": "Sucesso|Em processamento|Falha|Erro",
  "chavePix": "pix_key_used",
  "valor": 1000,
  "horario": "2024-01-15T10:30:00Z",
  "pagador": {
    "nome": "Nome do Pagador",
    "codigoBanco": "bank_code",
    "cpf_cnpj": "***123456**"
  },
  "recebedor": {
    "nome": "Nome do Recebedor",
    "codigoBanco": "bank_code",
    "cpf_cnpj": "***654321**"
  }
}
```

**Status Possíveis:**
- `Sucesso`: Pagamento aprovado e processado
- `Em processamento`: Pagamento sendo processado
- `Falha`: Pagamento rejeitado
- `Erro`: Erro no processamento

### 2. PixOut - Transferências Enviadas

Evento disparado quando uma transferência PIX é enviada.

**Estrutura do Payload:**
```json
{
  "evento": "PixOut",
  "token": "event_token",
  "idEnvio": "send_id",
  "endToEndId": "E12345678...",
  "codigoTransacao": "internal_code",
  "status": "Sucesso|Em processamento|Falha|Erro",
  "chavePix": "destination_pix_key",
  "valor": -500,
  "horario": "2024-01-15T10:30:00Z",
  "recebedor": {
    "nome": "Nome do Destinatário",
    "codigoBanco": "bank_code",
    "cpf_cnpj": "***987654**"
  },
  "erro": {
    "origem": "Origem interna",
    "motivo": "Conta sem saldo"
  }
}
```

**Observações:**
- O valor é negativo para transferências enviadas
- Campo `erro` presente apenas em caso de falha
- `idEnvio` é o identificador único da transferência

### 3. PixInReversal - Estornos de Pagamentos Recebidos

Evento disparado quando um pagamento recebido é estornado.

**Estrutura do Payload:**
```json
{
  "evento": "PixInReversal",
  "token": "event_token",
  "idEnvio": "reversal_id",
  "endToEndId": "E12345678...",
  "codigoTransacao": "reversal_code",
  "status": "Sucesso|Em processamento|Falha",
  "chavePix": null,
  "valor": -1000,
  "horario": "2024-01-15T10:30:00Z",
  "recebedor": {
    "nome": "Nome do Recebedor do Estorno",
    "codigoBanco": "bank_code",
    "cpf_cnpj": "***123456**"
  }
}
```

### 4. PixOutReversalExternal - Estornos Externos de Transferências

Evento disparado quando uma transferência enviada é estornada externamente.

**Estrutura do Payload:**
```json
{
  "evento": "PixOutReversalExternal",
  "token": "event_token",
  "idEnvio": "original_send_id",
  "endToEndId": "E12345678...",
  "codigoTransacao": "reversal_code",
  "status": "Sucesso",
  "chavePix": null,
  "valor": 500,
  "horario": "2024-01-15T10:30:00Z",
  "recebedor": {
    "nome": null,
    "codigoBanco": "bank_code",
    "cpf_cnpj": "***987654**"
  },
  "erro": {
    "origem": "Origem externa",
    "motivo": "Pedido de reembolso"
  }
}
```

## Processamento de Transações

### Busca de Transações

O sistema utiliza múltiplas estratégias para encontrar transações:

1. **Busca direta por `externalId`**
2. **Busca por `codigoTransacao`** (se disponível)
3. **Busca em metadados** por vários identificadores Flow2Pay
4. **Busca por `endToEndId`** (se disponível)

```javascript
const transaction = await db.transaction.findFirst({
  where: {
    OR: [
      { externalId: txid },
      ...(codigoTransacao ? [{ externalId: codigoTransacao }] : []),
      { metadata: { path: ['txid'], equals: txid } },
      { metadata: { path: ['flow2pay_txid'], equals: txid } },
      ...(codigoTransacao ? [{ metadata: { path: ['codigoTransacao'], equals: codigoTransacao } }] : []),
      ...(endToEndId ? [{ endToEndId }] : [])
    ]
  }
});
```

### Mapeamento de Status

```javascript
// Flow2Pay -> Status Interno
switch (status) {
  case 'Sucesso':
    transactionStatus = TransactionStatus.APPROVED;
    break;
  case 'Em processamento':
    transactionStatus = TransactionStatus.PENDING;
    break;
  case 'Falha':
  case 'Erro':
    transactionStatus = TransactionStatus.REJECTED;
    break;
}
```

### Atualização de Metadados

O sistema enriquece os metadados da transação com informações do webhook:

```javascript
const enhancedMetadata = {
  ...transaction.metadata,
  flow2pay_webhook: payload,
  flow2pay_status: status,
  endToEndId,
  chavePix,
  horario,
  codigoTransacao,
  // Informações do pagador/recebedor
  payer: payload.pagador,
  receiver: payload.recebedor,
  // Timestamps
  updated_at: new Date().toISOString(),
  webhook_processed_at: new Date().toISOString()
};
```

## Webhooks de Saída

Após processar o webhook da Flow2Pay, o sistema dispara webhooks para as organizações:

### Eventos Disparados

- **PixIn Aprovado**: `pix.payment.completed`
- **PixOut Aprovado**: `pix.transfer.completed`
- **PixOut Rejeitado**: `pix.transfer.failed`
- **PixOut Atualizado**: `pix.transfer.updated`
- **Estornos**: `pix.reversal.completed`

### Estrutura do Payload de Saída

```javascript
{
  type: 'pix.payment.completed',
  payload: {
    id: transaction.id,
    organizationId: transaction.organizationId,
    amount: transaction.amount,
    status: transactionStatus,
    endToEndId,
    txid,
    type: 'PIX_IN',
    createdAt: transaction.createdAt,
    updatedAt: new Date(),
    paymentAt: new Date()
  },
  organizationId: transaction.organizationId,
  transactionId: transaction.id
}
```

## Tratamento de Estornos

### PixInReversal

1. Busca a transação original pelo `endToEndId`
2. Cria uma nova transação do tipo `REFUND`
3. Vincula à transação original via `originalTransactionId`
4. Dispara webhook de estorno

### PixOutReversalExternal

1. Busca a transação original pelo `endToEndId`
2. Cria uma nova transação do tipo `REFUND`
3. Inclui informações de erro se disponíveis
4. Dispara webhook de estorno

## Validações e Tratamento de Erros

### Validações de Entrada

1. **Formato JSON válido**
2. **Campo `evento` obrigatório**
3. **Tipo de evento válido**
4. **Token de autenticação (se configurado)**

### Tratamento de Erros

```javascript
// Transação não encontrada
if (!transaction) {
  logger.warn("Transaction not found for Flow2Pay webhook", {
    searchCriteria: {
      externalId: [txid, codigoTransacao].filter(Boolean),
      endToEndId,
      metadataFields: ['txid', 'flow2pay_txid', 'codigoTransacao']
    }
  });
}

// Erro no processamento
catch (error) {
  logger.error("Error processing Flow2Pay webhook", {
    error: error.message,
    stack: error.stack
  });
  throw error;
}
```

## Logs e Monitoramento

O sistema registra logs detalhados para cada webhook:

- **Recebimento**: Payload completo e headers
- **Processamento**: Status mapping e busca de transações
- **Atualização**: Mudanças de status e metadados
- **Webhooks de saída**: Eventos disparados
- **Erros**: Stack traces completos

## Configuração de Ambiente

```bash
# Token de autenticação dos webhooks Flow2Pay
FLOW2PAY_EVENT_TOKEN=your_event_token_here

# URL base para webhooks de saída (opcional)
NEXT_PUBLIC_SITE_URL=https://your-domain.com
```

## Teste do Endpoint

Use o script de teste incluído:

```bash
node scripts/test-flow2pay-webhook.js
```

O script testa todos os tipos de eventos com payloads realistas baseados na documentação da Flow2Pay.

## Migração do PixAPI

Este endpoint substitui a implementação anterior do projeto PixAPI (Encore.dev). As principais diferenças:

1. **Framework**: Encore.dev → Next.js
2. **Endpoint**: `/api/webhooks/flow2pay/baas` → `/baas`
3. **Validação**: Melhor tratamento de erros e logs
4. **Metadados**: Estrutura mais rica de metadados
5. **Webhooks**: Sistema de webhooks de saída aprimorado

## Considerações de Segurança

1. **Validação de Token**: Sempre configure `FLOW2PAY_EVENT_TOKEN`
2. **HTTPS**: Use sempre HTTPS em produção
3. **Rate Limiting**: Considere implementar rate limiting
4. **Logs**: Não registre informações sensíveis nos logs
5. **Validação de Origem**: Valide IPs de origem se necessário
