#!/usr/bin/env tsx
/**
 * <PERSON><PERSON><PERSON> to activate the existing PLUGGOU_PIX gateway
 */

// Load environment variables first
import { config } from 'dotenv';
config({ path: '.env.local' });

import { PrismaClient } from "@prisma/client";

const db = new PrismaClient();

const TARGET_ORGANIZATION_ID = "fC99w8SdDGbNJM_q0b2s5";

async function activatePluggouGateway() {
  try {
    console.log("🔍 Looking for PLUGGOU_PIX gateway...");

    // Find the existing gateway
    const gateway = await db.paymentGateway.findFirst({
      where: {
        type: "PLUGGOU_PIX",
        isGlobal: true,
      },
    });

    if (!gateway) {
      console.log("❌ No PLUGGOU_PIX gateway found");
      return;
    }

    console.log(`📍 Found gateway: ${gateway.id} (isActive: ${gateway.isActive})`);

    // Activate the gateway
    const updatedGateway = await db.paymentGateway.update({
      where: { id: gateway.id },
      data: {
        isActive: true,
        canReceive: true,
        canSend: true,
        priority: 0,
      },
    });

    console.log(`✅ Gateway activated: ${updatedGateway.id}`);

    // Check organization relationship
    let orgGateway = await db.organizationGateway.findFirst({
      where: {
        gatewayId: gateway.id,
        organizationId: TARGET_ORGANIZATION_ID,
      },
    });

    if (!orgGateway) {
      // Create organization relationship
      orgGateway = await db.organizationGateway.create({
        data: {
          gatewayId: gateway.id,
          organizationId: TARGET_ORGANIZATION_ID,
          isActive: true,
          isDefault: true,
          priority: 1,
        },
      });
      console.log(`✅ Created organization relationship: ${orgGateway.id}`);
    } else {
      // Activate existing relationship
      await db.organizationGateway.update({
        where: { id: orgGateway.id },
        data: {
          isActive: true,
          isDefault: true,
          priority: 1,
        },
      });
      console.log(`✅ Activated organization relationship: ${orgGateway.id}`);
    }

    // Verify
    const verification = await db.paymentGateway.findFirst({
      where: {
        type: "PLUGGOU_PIX",
        isActive: true,
        isGlobal: true,
      },
    });

    if (verification) {
      console.log("🎉 PLUGGOU_PIX gateway is now active and ready!");
    } else {
      console.log("❌ Something went wrong during activation");
    }

  } catch (error) {
    console.error("❌ Error activating gateway:", error);
  } finally {
    await db.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  activatePluggouGateway()
    .then(() => {
      console.log("✅ Script completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ Script failed:", error);
      process.exit(1);
    });
}

export { activatePluggouGateway };
