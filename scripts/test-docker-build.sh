#!/bin/bash

# Quick Docker build test script
# Tests the Docker build process with better error handling

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."

    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi

    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running"
        exit 1
    fi

    if [[ ! -f "apps/web/Dockerfile" ]]; then
        print_error "Dockerfile not found at apps/web/Dockerfile"
        exit 1
    fi

    print_success "Prerequisites check passed"
}

# Function to test Docker build
test_build() {
    local image_name="pluggou-test"
    local tag="test-$(date +%s)"

    print_status "Starting Docker build test..."
    print_status "Image: $image_name:$tag"

    # Build and capture output (without timeout on macOS)
    if docker build \
        -f apps/web/Dockerfile \
        -t "$image_name:$tag" \
        --progress=plain \
        . 2>&1 | tee /tmp/docker-build.log; then

        print_success "Docker build completed successfully!"

        # Test the built image
        print_status "Testing the built image..."
        if docker run --rm -d --name "test-$tag" -p 3001:3000 "$image_name:$tag" &> /dev/null; then
            sleep 10

            if curl -f http://localhost:3001/api/health &> /dev/null; then
                print_success "Container is running and healthy!"
                docker stop "test-$tag" &> /dev/null
            else
                print_warning "Container started but health check failed"
                docker stop "test-$tag" &> /dev/null || true
            fi
        else
            print_warning "Container failed to start"
        fi

        # Cleanup test image
        docker rmi "$image_name:$tag" &> /dev/null || true

        return 0
    else
        print_error "Docker build failed!"

        # Check for specific errors
        if grep -q "lockfile.*mismatch\|frozen.*lockfile\|@biomejs/biome" /tmp/docker-build.log; then
            print_warning "Detected lockfile-related errors in build log"
            print_warning "Try running: ./scripts/fix-lockfile.sh"
        fi

        if grep -q "turbo.json.*not found" /tmp/docker-build.log; then
            print_warning "Detected turbo.json not found error"
            print_warning "Make sure you're running from the root directory"
        fi

        if grep -q "ENOSPC\|no space left" /tmp/docker-build.log; then
            print_warning "Detected disk space issues"
            print_warning "Try running: docker system prune -f"
        fi

        print_status "Build log saved to: /tmp/docker-build.log"
        return 1
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Test Docker build process with error detection"
    echo ""
    echo "Options:"
    echo "  --no-test       Skip container testing after build"
    echo "  --keep-image    Don't remove test image after build"
    echo "  -h, --help      Show this help"
}

# Parse command line arguments
NO_TEST=0
KEEP_IMAGE=0

while [[ $# -gt 0 ]]; do
    case $1 in
        --no-test)
            NO_TEST=1
            shift
            ;;
        --keep-image)
            KEEP_IMAGE=1
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    print_status "Docker Build Test Starting..."

    check_prerequisites

    if test_build; then
        print_success "Docker build test completed successfully!"
        print_status "Your Docker setup is working correctly"
    else
        print_error "Docker build test failed"
        print_status "Check the error messages above for guidance"
        exit 1
    fi
}

# Run main function
main "$@"
