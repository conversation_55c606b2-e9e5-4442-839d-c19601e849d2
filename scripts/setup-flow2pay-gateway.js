#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to configure Flow2Pay as the default payment gateway
 * This script creates the Flow2Pay gateway and sets it as default for your organization
 */

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const { PrismaClient } = require('@prisma/client');
const { randomUUID } = require('crypto');

const prisma = new PrismaClient();

async function setupFlow2PayGateway() {
  try {
    console.log('🚀 Setting up Flow2Pay as default payment gateway...');

    // Flow2Pay credentials - replace with your actual credentials
    const flow2payCredentials = {
      clientId: process.env.FLOW2PAY_CLIENT_ID || 'your_flow2pay_client_id',
      clientSecret: process.env.FLOW2PAY_CLIENT_SECRET || 'your_flow2pay_client_secret',
      eventToken: process.env.FLOW2PAY_EVENT_TOKEN || 'your_flow2pay_event_token',
      apiUrl: process.env.FLOW2PAY_API_URL || 'https://pixv2.flow2pay.com.br'
    };

    // Check if credentials are properly configured
    if (flow2payCredentials.clientId === 'your_flow2pay_client_id') {
      console.warn('⚠️  Warning: Using placeholder credentials. Please set environment variables:');
      console.warn('   FLOW2PAY_CLIENT_ID');
      console.warn('   FLOW2PAY_CLIENT_SECRET');
      console.warn('   FLOW2PAY_EVENT_TOKEN');
      console.warn('   FLOW2PAY_API_URL (optional)');
    }

    // First, deactivate all existing default gateways
    console.log('📝 Deactivating existing default gateways...');
    await prisma.paymentGateway.updateMany({
      where: {
        isDefault: true
      },
      data: {
        isDefault: false
      }
    });

    // Check if Flow2Pay gateway already exists
    let flow2payGateway = await prisma.paymentGateway.findFirst({
      where: {
        type: 'FLOW2PAY'
      }
    });

    if (flow2payGateway) {
      console.log('📝 Updating existing Flow2Pay gateway...');
      flow2payGateway = await prisma.paymentGateway.update({
        where: {
          id: flow2payGateway.id
        },
        data: {
          name: 'Flow2Pay',
          isActive: true,
          isDefault: true,
          priority: 1,
          credentials: flow2payCredentials,
          canReceive: true,
          canSend: true,
          isGlobal: true
        }
      });
    } else {
      console.log('📝 Creating new Flow2Pay gateway...');
      flow2payGateway = await prisma.paymentGateway.create({
        data: {
          id: randomUUID(),
          name: 'Flow2Pay',
          type: 'FLOW2PAY',
          isActive: true,
          isDefault: true,
          priority: 1,
          credentials: flow2payCredentials,
          canReceive: true,
          canSend: true,
          isGlobal: true,
          pixChargePercentFee: 0,
          pixTransferPercentFee: 0,
          pixChargeFixedFee: 0,
          pixTransferFixedFee: 0
        }
      });
    }

    console.log('✅ Flow2Pay gateway configured successfully!');
    console.log(`   Gateway ID: ${flow2payGateway.id}`);
    console.log(`   Name: ${flow2payGateway.name}`);
    console.log(`   Type: ${flow2payGateway.type}`);
    console.log(`   Active: ${flow2payGateway.isActive}`);
    console.log(`   Default: ${flow2payGateway.isDefault}`);
    console.log(`   Can Receive: ${flow2payGateway.canReceive}`);
    console.log(`   Can Send: ${flow2payGateway.canSend}`);

    // Get your organization ID (you'll need to replace this with your actual organization ID)
    const organizationId = process.env.ORGANIZATION_ID;

    if (organizationId) {
      console.log(`📝 Configuring Flow2Pay for organization: ${organizationId}`);

      // Check if organization gateway assignment already exists
      let orgGateway = await prisma.organizationGateway.findFirst({
        where: {
          organizationId,
          gatewayId: flow2payGateway.id
        }
      });

      // Deactivate other default gateways for this organization
      await prisma.organizationGateway.updateMany({
        where: {
          organizationId,
          isDefault: true
        },
        data: {
          isDefault: false
        }
      });

      if (orgGateway) {
        console.log('📝 Updating existing organization gateway assignment...');
        await prisma.organizationGateway.update({
          where: {
            id: orgGateway.id
          },
          data: {
            isActive: true,
            isDefault: true,
            priority: 1
          }
        });
      } else {
        console.log('📝 Creating new organization gateway assignment...');
        await prisma.organizationGateway.create({
          data: {
            organizationId,
            gatewayId: flow2payGateway.id,
            isActive: true,
            isDefault: true,
            priority: 1
          }
        });
      }

      console.log('✅ Flow2Pay configured as default gateway for organization!');
    } else {
      console.log('⚠️  No ORGANIZATION_ID provided. Gateway created globally but not assigned to specific organization.');
      console.log('   To assign to your organization, run:');
      console.log(`   ORGANIZATION_ID=your_org_id node scripts/setup-flow2pay-gateway.js`);
    }

    console.log('\n🎯 Next steps:');
    console.log('1. Configure your Flow2Pay webhook URL in the Flow2Pay dashboard:');
    console.log(`   ${process.env.NEXT_PUBLIC_SITE_URL || 'https://your-domain.com'}/api/webhooks/flow2pay/baas`);
    console.log('2. Test the integration by creating a PIX payment');
    console.log('3. Monitor webhook events in your application logs');

  } catch (error) {
    console.error('❌ Error setting up Flow2Pay gateway:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the setup
setupFlow2PayGateway();
