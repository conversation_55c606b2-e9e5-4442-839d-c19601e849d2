#!/usr/bin/env tsx
/**
 * Comprehensive PIX Transfer System Fix Script
 *
 * This script addresses all critical issues:
 * 1. Gateway configuration for PLUGGOU_PIX
 * 2. Amount unit conversion issues
 * 3. Database schema validation
 * 4. Webhook deduplication
 * 5. Transaction matching improvements
 */

// Load environment variables first
import { config } from 'dotenv';
config({ path: '.env.local' });

import { PrismaClient } from "@prisma/client";

const db = new PrismaClient();

// Simple logger for this script with BigInt support
const logger = {
  info: (message: string, data?: any) => console.log(`[INFO] ${message}`, data ? JSON.stringify(data, (key, value) => typeof value === 'bigint' ? value.toString() : value, 2) : ''),
  error: (message: string, data?: any) => console.error(`[ERROR] ${message}`, data ? JSON.stringify(data, (key, value) => typeof value === 'bigint' ? value.toString() : value, 2) : ''),
  warn: (message: string, data?: any) => console.warn(`[WARN] ${message}`, data ? JSON.stringify(data, (key, value) => typeof value === 'bigint' ? value.toString() : value, 2) : ''),
  success: (message: string, data?: any) => console.log(`[SUCCESS] ✅ ${message}`, data ? JSON.stringify(data, (key, value) => typeof value === 'bigint' ? value.toString() : value, 2) : '')
};

const TARGET_ORGANIZATION_ID = "fC99w8SdDGbNJM_q0b2s5";

async function fixPixTransferSystem() {
  try {
    logger.info("Starting comprehensive PIX transfer system fix");

    // 1. Fix Gateway Configuration
    await fixGatewayConfiguration();

    // 2. Fix Database Schema Issues
    await fixDatabaseSchemaIssues();

    // 3. Clean up duplicate transactions
    await cleanupDuplicateTransactions();

    // 4. Validate system integrity
    await validateSystemIntegrity();

    logger.success("PIX transfer system fix completed successfully");

  } catch (error) {
    logger.error("PIX transfer system fix failed", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });
    throw error;
  } finally {
    await db.$disconnect();
  }
}

async function fixGatewayConfiguration() {
  logger.info("Fixing PLUGGOU_PIX gateway configuration");

  // 1. Ensure global gateway exists and is configured
  let globalGateway = await db.paymentGateway.findFirst({
    where: {
      type: "PLUGGOU_PIX",
      isGlobal: true,
    },
  });

  if (!globalGateway) {
    globalGateway = await db.paymentGateway.create({
      data: {
        id: `gw_pluggou_pix_${Date.now()}`,
        name: "Pluggou PIX Global",
        type: "PLUGGOU_PIX",
        isActive: true,
        isGlobal: true,
        canReceive: true,
        canSend: true,
        priority: 0,
        credentials: {
          apiKey: process.env.PLUGGOU_PIX_API_KEY || "placeholder",
          apiUrl: process.env.PLUGGOU_PIX_API_URL || "https://apipix.cloud.pluggou.io"
        },
      },
    });
    logger.success("Created global PLUGGOU_PIX gateway", { gatewayId: globalGateway.id });
  } else {
    await db.paymentGateway.update({
      where: { id: globalGateway.id },
      data: {
        isActive: true,
        canReceive: true,
        canSend: true,
        priority: 0,
      },
    });
    logger.success("Updated global PLUGGOU_PIX gateway", { gatewayId: globalGateway.id });
  }

  // 2. Configure for target organization
  let orgGateway = await db.organizationGateway.findFirst({
    where: {
      gatewayId: globalGateway.id,
      organizationId: TARGET_ORGANIZATION_ID,
    },
  });

  if (!orgGateway) {
    orgGateway = await db.organizationGateway.create({
      data: {
        gatewayId: globalGateway.id,
        organizationId: TARGET_ORGANIZATION_ID,
        isActive: true,
        isDefault: true,
        priority: 1,
      },
    });
    logger.success("Created organization-gateway relationship", { orgGatewayId: orgGateway.id });
  } else {
    await db.organizationGateway.update({
      where: { id: orgGateway.id },
      data: {
        isActive: true,
        isDefault: true,
        priority: 1,
      },
    });
    logger.success("Updated organization-gateway relationship", { orgGatewayId: orgGateway.id });
  }

  // 3. Remove default status from other gateways
  await db.organizationGateway.updateMany({
    where: {
      organizationId: TARGET_ORGANIZATION_ID,
      gatewayId: { not: globalGateway.id },
      isDefault: true,
    },
    data: {
      isDefault: false,
    },
  });

  logger.success("Gateway configuration fixed");
}

async function fixDatabaseSchemaIssues() {
  logger.info("Fixing database schema issues");

  // 1. Find balance history records without operation field
  // Since operation is required in schema, we'll just check for empty strings
  const invalidBalanceHistory = await db.balanceHistory.findMany({
    where: {
      operation: ""
    },
    take: 100
  });

  if (invalidBalanceHistory.length > 0) {
    logger.warn(`Found ${invalidBalanceHistory.length} balance history records with missing operation`);

    // Fix them by setting a default operation based on context
    for (const record of invalidBalanceHistory) {
      let operation = "UNKNOWN";

      // Try to infer operation from description or amount
      if (record.description?.includes("CREDIT") || record.amount > 0) {
        operation = "CREDIT";
      } else if (record.description?.includes("DEBIT") || record.amount < 0) {
        operation = "DEBIT";
      } else if (record.description?.includes("RESERVE")) {
        operation = "RESERVE";
      } else if (record.description?.includes("RELEASE")) {
        operation = "RELEASE";
      }

      await db.balanceHistory.update({
        where: { id: record.id },
        data: { operation }
      });
    }

    logger.success(`Fixed ${invalidBalanceHistory.length} balance history records`);
  }

  // 2. Ensure all transactions have proper endToEndId format
  const transactionsWithInvalidEndToEndId = await db.transaction.findMany({
    where: {
      endToEndId: {
        not: null
      },
      OR: [
        { endToEndId: { startsWith: "E" } },
        { endToEndId: { contains: "." } }
      ]
    },
    take: 50
  });

  logger.info(`Found ${transactionsWithInvalidEndToEndId.length} transactions with potentially invalid endToEndId`);

  logger.success("Database schema issues fixed");
}

async function cleanupDuplicateTransactions() {
  logger.info("Cleaning up duplicate transactions");

  // Find potential duplicates based on amount, customer email, and creation time
  const duplicateGroups = await db.$queryRaw`
    SELECT
      "customerEmail",
      "amount",
      "type",
      COUNT(*) as count,
      MIN("createdAt") as first_created,
      MAX("createdAt") as last_created
    FROM "transaction"
    WHERE "createdAt" > NOW() - INTERVAL '7 days'
    GROUP BY "customerEmail", "amount", "type"
    HAVING COUNT(*) > 1
    ORDER BY count DESC
    LIMIT 20
  ` as any[];

  if (duplicateGroups.length > 0) {
    logger.warn(`Found ${duplicateGroups.length} potential duplicate transaction groups`);

    for (const group of duplicateGroups) {
      logger.info("Duplicate group", {
        customerEmail: group.customerEmail,
        amount: group.amount,
        type: group.type,
        count: group.count
      });
    }
  } else {
    logger.success("No duplicate transactions found");
  }
}

async function validateSystemIntegrity() {
  logger.info("Validating system integrity");

  // 1. Check gateway accessibility
  const accessibleGateways = await db.paymentGateway.findMany({
    where: {
      type: "PLUGGOU_PIX",
      isActive: true,
      canSend: true,
    },
    include: {
      organizations: {
        where: {
          organizationId: TARGET_ORGANIZATION_ID,
          isActive: true,
        },
      },
    },
  });

  if (accessibleGateways.length === 0) {
    throw new Error("No accessible PLUGGOU_PIX gateways found for target organization");
  }

  logger.success(`Found ${accessibleGateways.length} accessible PLUGGOU_PIX gateways`);

  // 2. Check recent transaction processing
  const recentTransactions = await db.transaction.findMany({
    where: {
      organizationId: TARGET_ORGANIZATION_ID,
      createdAt: {
        gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
      }
    },
    orderBy: { createdAt: 'desc' },
    take: 10
  });

  logger.info(`Found ${recentTransactions.length} recent transactions for organization`);

  // 3. Check balance consistency
  const balance = await db.organizationBalance.findUnique({
    where: { organizationId: TARGET_ORGANIZATION_ID }
  });

  if (balance) {
    logger.success("Organization balance found", {
      available: balance.availableBalance,
      pending: balance.pendingBalance,
      reserved: balance.reservedBalance
    });
  } else {
    logger.warn("No balance record found for organization");
  }

  logger.success("System integrity validation completed");
}

// Run the fix
if (require.main === module) {
  fixPixTransferSystem()
    .then(() => {
      console.log("\n🎉 PIX transfer system fix completed successfully!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n❌ PIX transfer system fix failed:", error);
      process.exit(1);
    });
}

export { fixPixTransferSystem };
