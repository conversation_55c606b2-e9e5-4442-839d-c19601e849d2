#!/bin/bash

# Debug Script for Prisma Generation Issues
# This script helps debug Prisma generation problems in Docker environments

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="${IMAGE_NAME:-pluggou-web-debug}"
TAG="${TAG:-debug}"
DOCKERFILE_PATH="${DOCKERFILE_PATH:-apps/web/Dockerfile}"
BUILD_CONTEXT="${BUILD_CONTEXT:-.}"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to test Prisma generation locally
test_local_prisma_generation() {
    print_status "Testing Prisma generation locally..."

    # Check if we're in the right directory
    if [ ! -f "turbo.json" ] || [ ! -f "package.json" ]; then
        print_error "Please run this script from the root directory of the monorepo."
        exit 1
    fi

    # Set temporary DATABASE_URL
    export DATABASE_URL="***********************************/dummy"

    print_status "Current environment:"
    echo "Node.js version: $(node --version)"
    echo "npm version: $(npm --version)"
    echo "pnpm version: $(pnpm --version)"
    echo "DATABASE_URL: $DATABASE_URL"

    print_status "Checking database package..."
    if [ ! -d "packages/database" ]; then
        print_error "Database package directory not found!"
        exit 1
    fi

    print_status "Database package contents:"
    ls -la packages/database/

    print_status "Prisma schema:"
    cat packages/database/prisma/schema.prisma

    print_status "Database package.json:"
    cat packages/database/package.json

    print_status "Testing Prisma CLI availability..."
    cd packages/database
    if pnpm exec prisma --version; then
        print_success "Prisma CLI is available"
    else
        print_error "Prisma CLI is not available"
        return 1
    fi

    print_status "Testing direct Prisma generation..."
    echo "Running: pnpm exec prisma generate --schema=./prisma/schema.prisma --no-hints"
    if pnpm exec prisma generate --schema=./prisma/schema.prisma --no-hints; then
        print_success "Direct Prisma generation succeeded"
        echo "Checking generated files..."
        ls -la node_modules/.prisma/ 2>/dev/null || echo "No .prisma directory in local node_modules"
    else
        print_error "Direct Prisma generation failed"
        return 1
    fi

    cd ../..

    print_status "Testing Turbo Prisma generation..."
    echo "Running: pnpm turbo run generate --filter=@repo/database"
    if pnpm turbo run generate --filter=@repo/database; then
        print_success "Turbo Prisma generation succeeded"
        echo "Checking generated files after Turbo..."
        find . -name ".prisma" -type d 2>/dev/null || echo "No .prisma directories found"
    else
        print_error "Turbo Prisma generation failed"
        return 1
    fi

    print_success "Local Prisma generation test completed successfully!"
}

# Function to build debug Docker image
build_debug_docker_image() {
    print_status "Building debug Docker image..."

    # Create a debug Dockerfile that stops at the Prisma generation step
    cat > Dockerfile.debug << 'EOF'
# Use Node.js slim image with essential build tools for Prisma compatibility
FROM node:22-slim AS base

# Install system dependencies required for Prisma and native compilation
RUN apt-get update && apt-get install -y \
    # Essential build tools
    build-essential \
    gcc \
    g++ \
    make \
    python3 \
    python3-pip \
    # Additional libraries that Prisma might need
    libc6-dev \
    libssl-dev \
    pkg-config \
    # SSL/TLS support
    ca-certificates \
    openssl \
    # Process management
    dumb-init \
    # Git (sometimes needed for npm packages)
    git \
    # Cleanup
    && rm -rf /var/lib/apt/lists/* \
    && corepack enable

# Set pnpm environment
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

# Install turbo globally in base image for better caching
RUN pnpm add -g turbo

# Set working directory
WORKDIR /app

# ============================================
# INSTALLER STAGE - Install dependencies
# ============================================
FROM base AS installer

# Copy workspace configuration files
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml turbo.json ./

# Copy all package.json files explicitly for better dependency resolution
COPY apps/web/package.json ./apps/web/package.json
COPY packages/ai/package.json ./packages/ai/package.json
COPY packages/api/package.json ./packages/api/package.json
COPY packages/auth/package.json ./packages/auth/package.json
COPY packages/database/package.json ./packages/database/package.json
COPY packages/i18n/package.json ./packages/i18n/package.json
COPY packages/logs/package.json ./packages/logs/package.json
COPY packages/mail/package.json ./packages/mail/package.json
COPY packages/payments/package.json ./packages/payments/package.json
COPY packages/queue/package.json ./packages/queue/package.json
COPY packages/shared/package.json ./packages/shared/package.json
COPY packages/storage/package.json ./packages/storage/package.json
COPY packages/utils/package.json ./packages/utils/package.json
COPY config/package.json ./config/package.json
COPY tooling/scripts/package.json ./tooling/scripts/package.json
COPY tooling/tailwind/package.json ./tooling/tailwind/package.json
COPY tooling/typescript/package.json ./tooling/typescript/package.json

# Set environment variables to optimize installation
ENV CYPRESS_INSTALL_BINARY=0
ENV PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1
ENV NEXT_TELEMETRY_DISABLED=1
ENV TURBO_TELEMETRY_DISABLED=1
# Memory optimization for package installation
ENV NODE_OPTIONS="--max-old-space-size=4096"
# Prisma environment variables for generation
ENV PRISMA_SKIP_POSTINSTALL_GENERATE=true
ENV PRISMA_GENERATE_SKIP_AUTOINSTALL=false
# Ensure proper binary target detection
ENV PRISMA_CLI_BINARY_TARGETS="debian-openssl-3.0.x"

# Install dependencies with optimizations
RUN --mount=type=cache,id=pnpm,target=/pnpm/store \
    echo "Installing dependencies..." && \
    pnpm install --frozen-lockfile --prefer-offline || \
    pnpm install --prefer-offline

# Copy database package source for Prisma generation
COPY packages/database/ ./packages/database/

# Set temporary DATABASE_URL for Prisma generation
ENV DATABASE_URL="***********************************/dummy"

# Debug Prisma generation step by step
RUN echo "=== PRISMA DEBUG CONTAINER ===" && \
    echo "This container is for debugging Prisma generation issues" && \
    echo "System info:" && \
    uname -a && \
    echo "Node.js: $(node --version)" && \
    echo "pnpm: $(pnpm --version)" && \
    echo "Build tools:" && \
    which gcc g++ python3 make && \
    echo "Environment:" && \
    env | grep -E "(NODE|PRISMA|DATABASE)" && \
    echo "Database package:" && \
    ls -la packages/database/ && \
    echo "Prisma schema:" && \
    cat packages/database/prisma/schema.prisma && \
    echo "=== READY FOR INTERACTIVE DEBUGGING ==="

# Set entrypoint to bash for interactive debugging
ENTRYPOINT ["/bin/bash"]
EOF

    print_status "Building debug Docker image..."
    docker build -f Dockerfile.debug -t "${IMAGE_NAME}:${TAG}" .

    print_success "Debug Docker image built: ${IMAGE_NAME}:${TAG}"
    print_status "You can now run: docker run -it ${IMAGE_NAME}:${TAG}"
    print_status "Inside the container, try:"
    print_status "  cd packages/database && pnpm exec prisma generate --schema=./prisma/schema.prisma --no-hints"
    print_status "  pnpm turbo run generate --filter=@repo/database"

    # Cleanup
    rm -f Dockerfile.debug
}

# Function to run interactive debug session
run_debug_session() {
    print_status "Starting interactive debug session..."

    if ! docker image inspect "${IMAGE_NAME}:${TAG}" >/dev/null 2>&1; then
        print_warning "Debug image not found. Building it first..."
        build_debug_docker_image
    fi

    print_status "Starting interactive container..."
    print_status "You can test Prisma generation manually inside the container."
    print_status "Try these commands:"
    print_status "  cd packages/database"
    print_status "  pnpm exec prisma --version"
    print_status "  pnpm exec prisma generate --schema=./prisma/schema.prisma --no-hints"
    print_status "  cd ../.."
    print_status "  pnpm turbo run generate --filter=@repo/database"

    docker run -it --rm "${IMAGE_NAME}:${TAG}"
}

# Function to clean up debug resources
cleanup_debug() {
    print_status "Cleaning up debug resources..."

    docker rmi "${IMAGE_NAME}:${TAG}" 2>/dev/null || true
    rm -f Dockerfile.debug

    print_success "Debug cleanup completed"
}

# Main function
main() {
    case "${1:-}" in
        "local")
            test_local_prisma_generation
            ;;
        "build")
            build_debug_docker_image
            ;;
        "run")
            run_debug_session
            ;;
        "cleanup")
            cleanup_debug
            ;;
        "locations")
            print_status "Testing Prisma file generation locations..."
            ./scripts/test-prisma-locations.sh all
            ;;
        "all")
            print_status "Running complete debug workflow..."
            test_local_prisma_generation
            build_debug_docker_image
            print_status "Debug image ready. Run './scripts/debug-prisma-generation.sh run' to start interactive session."
            print_status "You can also run './scripts/test-prisma-locations.sh all' to test file locations."
            ;;
        *)
            echo "Usage: $0 {local|build|run|cleanup|locations|all}"
            echo ""
            echo "Commands:"
            echo "  local     - Test Prisma generation locally"
            echo "  build     - Build debug Docker image"
            echo "  run       - Run interactive debug session"
            echo "  cleanup   - Clean up debug resources"
            echo "  locations - Test Prisma file generation locations"
            echo "  all       - Run local test and build debug image"
            echo ""
            echo "Example workflow:"
            echo "  $0 local     # Test locally first"
            echo "  $0 build     # Build debug image"
            echo "  $0 run       # Debug interactively"
            echo "  $0 cleanup   # Clean up when done"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
