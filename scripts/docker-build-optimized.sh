#!/bin/bash

# Optimized Docker Build Script for Memory-Intensive Builds
# This script provides enhanced memory management and build optimization

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="${IMAGE_NAME:-pluggou-web}"
TAG="${TAG:-latest}"
DOCKERFILE_PATH="${DOCKERFILE_PATH:-apps/web/Dockerfile}"
BUILD_CONTEXT="${BUILD_CONTEXT:-.}"
PLATFORM="${PLATFORM:-linux/amd64}"

# Memory optimization settings
MAX_MEMORY="${MAX_MEMORY:-12g}"
BUILD_MEMORY="${BUILD_MEMORY:-10g}"
SWAP_SIZE="${SWAP_SIZE:-4g}"

# Build options
USE_BUILDKIT="${USE_BUILDKIT:-1}"
ENABLE_CACHE="${ENABLE_CACHE:-1}"
PARALLEL_BUILDS="${PARALLEL_BUILDS:-1}"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check system resources
check_system_resources() {
    print_status "Checking system resources..."
    
    # Check available memory
    AVAILABLE_MEMORY=$(free -m | awk 'NR==2{printf "%.0f", $7/1024}')
    TOTAL_MEMORY=$(free -m | awk 'NR==2{printf "%.0f", $2/1024}')
    
    print_status "Total memory: ${TOTAL_MEMORY}GB"
    print_status "Available memory: ${AVAILABLE_MEMORY}GB"
    
    if [ "$AVAILABLE_MEMORY" -lt 8 ]; then
        print_warning "Low available memory detected (${AVAILABLE_MEMORY}GB). Build may fail."
        print_warning "Consider closing other applications or increasing system memory."
    fi
    
    # Check disk space
    AVAILABLE_DISK=$(df -BG . | awk 'NR==2 {print $4}' | sed 's/G//')
    print_status "Available disk space: ${AVAILABLE_DISK}GB"
    
    if [ "$AVAILABLE_DISK" -lt 10 ]; then
        print_warning "Low disk space detected (${AVAILABLE_DISK}GB). Build may fail."
    fi
}

# Function to optimize Docker daemon settings
optimize_docker_daemon() {
    print_status "Optimizing Docker daemon settings..."
    
    # Check if Docker daemon is running
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker daemon is not running. Please start Docker and try again."
        exit 1
    fi
    
    # Set Docker build memory limit
    export DOCKER_BUILDKIT=1
    export BUILDKIT_PROGRESS=plain
}

# Function to clean up before build
cleanup_before_build() {
    print_status "Cleaning up before build..."
    
    # Remove dangling images
    docker image prune -f >/dev/null 2>&1 || true
    
    # Remove unused build cache (keep recent)
    docker builder prune --filter until=24h -f >/dev/null 2>&1 || true
    
    print_status "Cleanup completed"
}

# Function to build with memory optimization
build_with_memory_optimization() {
    print_status "Starting optimized Docker build..."
    print_status "Image: ${IMAGE_NAME}:${TAG}"
    print_status "Dockerfile: ${DOCKERFILE_PATH}"
    print_status "Context: ${BUILD_CONTEXT}"
    print_status "Platform: ${PLATFORM}"
    print_status "Max memory: ${MAX_MEMORY}"
    
    # Build arguments for memory optimization
    BUILD_ARGS=(
        "--file" "$DOCKERFILE_PATH"
        "--tag" "${IMAGE_NAME}:${TAG}"
        "--platform" "$PLATFORM"
        "--progress" "plain"
        "--memory" "$BUILD_MEMORY"
        "--memory-swap" "$((${BUILD_MEMORY%g} + ${SWAP_SIZE%g}))g"
    )
    
    # Add cache options if enabled
    if [ "$ENABLE_CACHE" = "1" ]; then
        BUILD_ARGS+=(
            "--cache-from" "type=local,src=/tmp/.buildx-cache"
            "--cache-to" "type=local,dest=/tmp/.buildx-cache-new,mode=max"
        )
    fi
    
    # Add no-cache option if requested
    if [ "${NO_CACHE:-0}" = "1" ]; then
        BUILD_ARGS+=("--no-cache")
    fi
    
    # Execute build with error handling
    if docker buildx build "${BUILD_ARGS[@]}" "$BUILD_CONTEXT"; then
        print_success "Docker build completed successfully!"
        
        # Move cache if using cache
        if [ "$ENABLE_CACHE" = "1" ]; then
            rm -rf /tmp/.buildx-cache
            mv /tmp/.buildx-cache-new /tmp/.buildx-cache 2>/dev/null || true
        fi
        
        return 0
    else
        print_error "Docker build failed!"
        return 1
    fi
}

# Function to retry build with reduced settings
retry_build_with_reduced_settings() {
    print_warning "Retrying build with reduced memory settings..."
    
    # Reduce memory settings
    REDUCED_MEMORY="6g"
    REDUCED_SWAP="2g"
    
    BUILD_ARGS=(
        "--file" "$DOCKERFILE_PATH"
        "--tag" "${IMAGE_NAME}:${TAG}"
        "--platform" "$PLATFORM"
        "--progress" "plain"
        "--memory" "$REDUCED_MEMORY"
        "--memory-swap" "$((${REDUCED_MEMORY%g} + ${REDUCED_SWAP%g}))g"
        "--no-cache"
    )
    
    if docker buildx build "${BUILD_ARGS[@]}" "$BUILD_CONTEXT"; then
        print_success "Docker build completed with reduced settings!"
        return 0
    else
        print_error "Docker build failed even with reduced settings!"
        return 1
    fi
}

# Main execution
main() {
    print_status "Starting optimized Docker build process..."
    
    # Check if we're in the right directory
    if [ ! -f "turbo.json" ] || [ ! -f "package.json" ]; then
        print_error "Please run this script from the root directory of the monorepo."
        exit 1
    fi
    
    # Check system resources
    check_system_resources
    
    # Optimize Docker daemon
    optimize_docker_daemon
    
    # Cleanup before build
    cleanup_before_build
    
    # Attempt build with optimization
    if ! build_with_memory_optimization; then
        print_warning "Initial build failed, attempting retry with reduced settings..."
        if ! retry_build_with_reduced_settings; then
            print_error "All build attempts failed. Please check the logs above."
            exit 1
        fi
    fi
    
    print_success "Build process completed successfully!"
    print_status "Image created: ${IMAGE_NAME}:${TAG}"
    print_status "You can now run: docker run -p 3000:3000 ${IMAGE_NAME}:${TAG}"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        --tag)
            TAG="$2"
            shift 2
            ;;
        --memory)
            MAX_MEMORY="$2"
            BUILD_MEMORY="$2"
            shift 2
            ;;
        --no-cache)
            NO_CACHE=1
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --name NAME       Set image name (default: pluggou-web)"
            echo "  --tag TAG         Set image tag (default: latest)"
            echo "  --memory SIZE     Set memory limit (default: 10g)"
            echo "  --no-cache        Build without cache"
            echo "  --help            Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Run main function
main
