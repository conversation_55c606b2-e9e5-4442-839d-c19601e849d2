#!/bin/bash

# Test Script for Prisma File Location Detection
# This script helps identify where Prisma generates its files in different environments

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to test Prisma generation locally and check file locations
test_local_prisma_locations() {
    print_status "Testing Prisma file generation locations locally..."

    # Check if we're in the right directory
    if [ ! -f "turbo.json" ] || [ ! -f "package.json" ]; then
        print_error "Please run this script from the root directory of the monorepo."
        exit 1
    fi

    # Set temporary DATABASE_URL
    export DATABASE_URL="***********************************/dummy"

    print_status "Current environment:"
    echo "Working directory: $(pwd)"
    echo "DATABASE_URL: $DATABASE_URL"

    # Clean any existing generated files
    print_status "Cleaning existing Prisma generated files..."
    rm -rf node_modules/.prisma 2>/dev/null || true
    rm -rf packages/database/node_modules/.prisma 2>/dev/null || true
    find . -name ".prisma" -type d -exec rm -rf {} + 2>/dev/null || true

    print_status "Testing Prisma generation in database package..."
    cd packages/database

    print_status "Current directory: $(pwd)"
    print_status "Prisma schema exists:"
    ls -la prisma/schema.prisma

    print_status "Generating Prisma Client..."
    echo "Running: pnpm exec prisma generate --schema=./prisma/schema.prisma --no-hints"
    if pnpm exec prisma generate --schema=./prisma/schema.prisma --no-hints; then
        print_success "Prisma generation completed"
    else
        print_error "Prisma generation failed"
        cd ../..
        return 1
    fi

    print_status "Checking where files were generated..."

    echo "=== LOCAL NODE_MODULES ==="
    if [ -d "node_modules/.prisma" ]; then
        print_success "Found .prisma in packages/database/node_modules/"
        ls -la node_modules/.prisma/
        if [ -d "node_modules/.prisma/client" ]; then
            print_success "Found client directory"
            ls -la node_modules/.prisma/client/
            echo "Query Engine binaries:"
            ls -la node_modules/.prisma/client/libquery_engine-*.so.node 2>/dev/null || echo "No binaries found"
        fi
    else
        print_warning "No .prisma directory in packages/database/node_modules/"
    fi

    cd ../..

    echo "=== ROOT NODE_MODULES ==="
    if [ -d "node_modules/.prisma" ]; then
        print_success "Found .prisma in root node_modules/"
        ls -la node_modules/.prisma/
        if [ -d "node_modules/.prisma/client" ]; then
            print_success "Found client directory"
            ls -la node_modules/.prisma/client/
            echo "Query Engine binaries:"
            ls -la node_modules/.prisma/client/libquery_engine-*.so.node 2>/dev/null || echo "No binaries found"
        fi
    else
        print_warning "No .prisma directory in root node_modules/"
    fi

    echo "=== SEARCHING EVERYWHERE ==="
    print_status "Searching for all .prisma directories:"
    find . -name ".prisma" -type d 2>/dev/null || echo "No .prisma directories found"

    print_status "Searching for all Query Engine binaries:"
    find . -name "libquery_engine-*.so.node" -type f 2>/dev/null || echo "No Query Engine binaries found"

    print_status "Searching for all Prisma Client directories:"
    find . -path "*/node_modules/.prisma/client" -type d 2>/dev/null || echo "No Prisma Client directories found"
}

# Function to test with Turbo
test_turbo_prisma_generation() {
    print_status "Testing Prisma generation with Turbo..."

    # Set temporary DATABASE_URL
    export DATABASE_URL="***********************************/dummy"

    # Clean any existing generated files
    print_status "Cleaning existing Prisma generated files..."
    rm -rf node_modules/.prisma 2>/dev/null || true
    rm -rf packages/database/node_modules/.prisma 2>/dev/null || true
    find . -name ".prisma" -type d -exec rm -rf {} + 2>/dev/null || true

    print_status "Running Turbo generate..."
    if pnpm turbo run generate --filter=@repo/database --verbose; then
        print_success "Turbo Prisma generation completed"
    else
        print_error "Turbo Prisma generation failed"
        return 1
    fi

    print_status "Checking where Turbo generated files..."

    echo "=== AFTER TURBO GENERATION ==="
    print_status "Root node_modules/.prisma:"
    ls -la node_modules/.prisma/ 2>/dev/null || echo "Not found"

    print_status "Database package node_modules/.prisma:"
    ls -la packages/database/node_modules/.prisma/ 2>/dev/null || echo "Not found"

    print_status "All .prisma directories:"
    find . -name ".prisma" -type d 2>/dev/null || echo "None found"
}

# Function to create a Docker test container for file location testing
test_docker_prisma_locations() {
    print_status "Testing Prisma file locations in Docker..."

    # Create a minimal test Dockerfile
    cat > Dockerfile.prisma-test << 'EOF'
FROM node:22-slim AS base

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    g++ \
    make \
    python3 \
    python3-pip \
    libc6-dev \
    libssl-dev \
    pkg-config \
    ca-certificates \
    openssl \
    git \
    && rm -rf /var/lib/apt/lists/* \
    && corepack enable

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

RUN pnpm add -g turbo

WORKDIR /app

# Copy package files
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml turbo.json ./
COPY packages/database/package.json ./packages/database/package.json

# Install dependencies
ENV CYPRESS_INSTALL_BINARY=0
ENV PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1
ENV NEXT_TELEMETRY_DISABLED=1
ENV TURBO_TELEMETRY_DISABLED=1
ENV NODE_OPTIONS="--max-old-space-size=4096"
ENV PRISMA_SKIP_POSTINSTALL_GENERATE=true

RUN --mount=type=cache,id=pnpm,target=/pnpm/store \
    pnpm install --frozen-lockfile --prefer-offline || \
    pnpm install --prefer-offline

# Copy database source
COPY packages/database/ ./packages/database/

# Set DATABASE_URL
ENV DATABASE_URL="***********************************/dummy"

# Test Prisma generation and file locations
RUN echo "=== DOCKER PRISMA LOCATION TEST ===" && \
    echo "Testing direct Prisma generation..." && \
    cd packages/database && \
    echo "Running: pnpm exec prisma generate --schema=./prisma/schema.prisma --no-hints" && \
    pnpm exec prisma generate --schema=./prisma/schema.prisma --no-hints && \
    echo "Checking file locations..." && \
    echo "Local node_modules:" && \
    ls -la node_modules/.prisma/ 2>/dev/null || echo "Not found" && \
    cd ../.. && \
    echo "Root node_modules:" && \
    ls -la node_modules/.prisma/ 2>/dev/null || echo "Not found" && \
    echo "All .prisma directories:" && \
    find . -name ".prisma" -type d 2>/dev/null || echo "None found" && \
    echo "All Query Engine binaries:" && \
    find . -name "libquery_engine-*.so.node" -type f 2>/dev/null || echo "None found"

ENTRYPOINT ["/bin/bash"]
EOF

    print_status "Building Docker test image..."
    docker build -f Dockerfile.prisma-test -t prisma-location-test:latest .

    print_status "Running Docker test container..."
    docker run --rm prisma-location-test:latest -c "
        echo '=== INTERACTIVE DOCKER TEST ===' &&
        echo 'Current directory:' && pwd &&
        echo 'Searching for .prisma directories:' &&
        find . -name '.prisma' -type d 2>/dev/null || echo 'None found' &&
        echo 'Searching for Query Engine binaries:' &&
        find . -name 'libquery_engine-*.so.node' -type f 2>/dev/null || echo 'None found' &&
        echo 'Root node_modules contents:' &&
        ls -la node_modules/ | grep prisma || echo 'No Prisma-related directories' &&
        echo 'Database package node_modules contents:' &&
        ls -la packages/database/node_modules/ | grep prisma || echo 'No Prisma-related directories'
    "

    # Cleanup
    rm -f Dockerfile.prisma-test
}

# Function to compare different generation methods
compare_generation_methods() {
    print_status "Comparing different Prisma generation methods..."

    export DATABASE_URL="***********************************/dummy"

    echo "=== METHOD 1: Direct prisma generate ==="
    rm -rf node_modules/.prisma packages/database/node_modules/.prisma 2>/dev/null || true
    cd packages/database
    pnpm exec prisma generate --schema=./prisma/schema.prisma
    echo "Files generated in:"
    find ../.. -name ".prisma" -type d 2>/dev/null
    cd ../..

    echo "=== METHOD 2: Turbo generate ==="
    rm -rf node_modules/.prisma packages/database/node_modules/.prisma 2>/dev/null || true
    pnpm turbo run generate --filter=@repo/database
    echo "Files generated in:"
    find . -name ".prisma" -type d 2>/dev/null

    echo "=== METHOD 3: pnpm run generate in database package ==="
    rm -rf node_modules/.prisma packages/database/node_modules/.prisma 2>/dev/null || true
    cd packages/database
    pnpm run generate
    echo "Files generated in:"
    find ../.. -name ".prisma" -type d 2>/dev/null
    cd ../..
}

# Main function
main() {
    case "${1:-}" in
        "local")
            test_local_prisma_locations
            ;;
        "turbo")
            test_turbo_prisma_generation
            ;;
        "docker")
            test_docker_prisma_locations
            ;;
        "compare")
            compare_generation_methods
            ;;
        "all")
            print_status "Running comprehensive Prisma location tests..."
            test_local_prisma_locations
            echo ""
            test_turbo_prisma_generation
            echo ""
            compare_generation_methods
            ;;
        *)
            echo "Usage: $0 {local|turbo|docker|compare|all}"
            echo ""
            echo "Commands:"
            echo "  local    - Test direct Prisma generation locally"
            echo "  turbo    - Test Turbo-based Prisma generation"
            echo "  docker   - Test Prisma generation in Docker"
            echo "  compare  - Compare different generation methods"
            echo "  all      - Run all tests"
            echo ""
            echo "This script helps identify where Prisma generates its files"
            echo "in different environments and with different methods."
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
