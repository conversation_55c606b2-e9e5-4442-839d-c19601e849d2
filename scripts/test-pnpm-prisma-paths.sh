#!/bin/bash

# Test Script for pnpm Prisma Path Detection
# This script tests the exact logic used in the Docker verification step

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to test the exact Docker verification logic
test_docker_verification_logic() {
    print_status "Testing Docker verification logic locally..."
    
    # Check if we're in the right directory
    if [ ! -f "turbo.json" ] || [ ! -f "package.json" ]; then
        print_error "Please run this script from the root directory of the monorepo."
        exit 1
    fi
    
    # Set temporary DATABASE_URL and generate Prisma Client
    export DATABASE_URL="***********************************/dummy"
    
    print_status "Generating Prisma Client first..."
    cd packages/database
    pnpm exec prisma generate --schema=./prisma/schema.prisma
    cd ../..
    
    print_status "Now testing the Docker verification logic..."
    
    # This is the exact logic from the Dockerfile
    echo "=== PRISMA CLIENT VERIFICATION START ==="
    echo "Searching for Prisma Client files in pnpm workspace..."
    echo "1. Searching for .prisma directories in pnpm store:"
    
    PRISMA_DIRS=$(find node_modules/.pnpm -name ".prisma" -type d 2>/dev/null || true)
    
    if [ -n "$PRISMA_DIRS" ]; then
        echo "   ✅ Found .prisma directories in pnpm store:"
        echo "$PRISMA_DIRS"
        
        for dir in $PRISMA_DIRS; do
            if [ -d "$dir/client" ]; then
                echo "   ✅ Found Prisma Client in: $dir/client"
                echo "   Contents:"
                ls -la "$dir/client/"
                echo "   Query Engine binaries:"
                ls -la "$dir/client/libquery_engine-*.so.node" 2>/dev/null || echo "   No binaries found"
                echo "   This would be copied to standard location in Docker..."
                
                # Test the copy operation (but don't actually do it to avoid conflicts)
                if [ -f "$dir/client/index.js" ] && [ -f "$dir/client/package.json" ]; then
                    print_success "Prisma Client files are valid and ready for copying"
                else
                    print_error "Prisma Client files appear incomplete"
                fi
                break
            fi
        done
    else
        echo "   No .prisma directories found in pnpm store"
        echo "2. Checking traditional locations as fallback:"
        
        if [ -d "packages/database/node_modules/.prisma/client" ]; then
            echo "   ✅ Found in packages/database/node_modules/.prisma/client"
            print_success "Would copy from database package location"
        elif [ -d "node_modules/.prisma/client" ]; then
            echo "   ✅ Already in standard location"
            print_success "Already in expected location"
        else
            echo "   ❌ Prisma Client not found in any location!"
            echo "   Searching entire filesystem for debugging:"
            find . -name ".prisma" -type d 2>/dev/null | head -10
            find . -name "libquery_engine-*.so.node" -type f 2>/dev/null | head -5
            print_error "Verification would fail"
            return 1
        fi
    fi
    
    echo "=== PRISMA CLIENT VERIFICATION END ==="
    print_success "Docker verification logic test completed successfully!"
}

# Function to show current pnpm structure
show_pnpm_structure() {
    print_status "Showing current pnpm structure..."
    
    echo "=== PNPM STRUCTURE ==="
    echo "Root node_modules structure:"
    ls -la node_modules/ | head -10
    
    echo ""
    echo "pnpm store structure:"
    if [ -d "node_modules/.pnpm" ]; then
        echo "Found .pnpm directory"
        ls -la node_modules/.pnpm/ | head -10
        
        echo ""
        echo "Searching for Prisma-related directories in pnpm store:"
        find node_modules/.pnpm -name "*prisma*" -type d 2>/dev/null | head -10
        
        echo ""
        echo "Searching for .prisma directories:"
        find node_modules/.pnpm -name ".prisma" -type d 2>/dev/null
    else
        echo "No .pnpm directory found"
    fi
    
    echo ""
    echo "Database package node_modules:"
    if [ -d "packages/database/node_modules" ]; then
        ls -la packages/database/node_modules/ | head -10
    else
        echo "No database package node_modules found"
    fi
}

# Function to clean and regenerate to test fresh state
test_fresh_generation() {
    print_status "Testing fresh Prisma generation..."
    
    # Clean existing generated files
    print_status "Cleaning existing Prisma files..."
    rm -rf node_modules/.prisma 2>/dev/null || true
    rm -rf packages/database/node_modules/.prisma 2>/dev/null || true
    find node_modules/.pnpm -name ".prisma" -type d -exec rm -rf {} + 2>/dev/null || true
    
    # Set environment and generate
    export DATABASE_URL="***********************************/dummy"
    
    print_status "Generating Prisma Client fresh..."
    cd packages/database
    pnpm exec prisma generate --schema=./prisma/schema.prisma
    cd ../..
    
    print_status "Checking where files were generated..."
    echo "Searching for .prisma directories:"
    find . -name ".prisma" -type d 2>/dev/null
    
    echo ""
    echo "Searching for Query Engine binaries:"
    find . -name "libquery_engine-*.so.node" -type f 2>/dev/null
    
    # Test verification logic
    test_docker_verification_logic
}

# Main function
main() {
    case "${1:-}" in
        "structure")
            show_pnpm_structure
            ;;
        "verify")
            test_docker_verification_logic
            ;;
        "fresh")
            test_fresh_generation
            ;;
        "all")
            print_status "Running comprehensive pnpm Prisma path tests..."
            show_pnpm_structure
            echo ""
            test_fresh_generation
            ;;
        *)
            echo "Usage: $0 {structure|verify|fresh|all}"
            echo ""
            echo "Commands:"
            echo "  structure - Show current pnpm directory structure"
            echo "  verify    - Test Docker verification logic"
            echo "  fresh     - Clean and test fresh generation"
            echo "  all       - Run all tests"
            echo ""
            echo "This script tests the pnpm-specific Prisma path detection"
            echo "logic used in the Docker verification step."
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
