#!/bin/bash

# Test Script for Prisma Docker Fix
# This script tests the Prisma Client initialization in Docker containers

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="${IMAGE_NAME:-pluggou-web}"
TAG="${TAG:-latest}"
CONTAINER_NAME="${CONTAINER_NAME:-prisma-test-container}"
TEST_PORT="${TEST_PORT:-3001}"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to cleanup previous test containers
cleanup_previous_tests() {
    print_status "Cleaning up previous test containers..."

    # Stop and remove existing test container
    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    docker rm "$CONTAINER_NAME" 2>/dev/null || true

    print_status "Cleanup completed"
}

# Function to check if image exists
check_image_exists() {
    print_status "Checking if Docker image exists: ${IMAGE_NAME}:${TAG}"

    if ! docker image inspect "${IMAGE_NAME}:${TAG}" >/dev/null 2>&1; then
        print_error "Docker image ${IMAGE_NAME}:${TAG} not found!"
        print_status "Please build the image first:"
        print_status "  ./scripts/docker-build-optimized.sh"
        print_status "  or"
        print_status "  make build-optimized"
        exit 1
    fi

    print_success "Docker image found"
}

# Function to test Prisma files in the image
test_prisma_files_in_image() {
    print_status "Testing Prisma files in Docker image..."

    # Create a temporary container to inspect files
    local temp_container="prisma-file-test"

    print_status "Creating temporary container to inspect Prisma files..."
    docker run --name "$temp_container" --entrypoint="" "${IMAGE_NAME}:${TAG}" sh -c "
        echo 'Checking Prisma Client files...' &&
        ls -la /app/node_modules/.prisma/client/ 2>/dev/null || echo 'Prisma client directory not found' &&
        echo '' &&
        echo 'Checking for Debian Linux Query Engine binary...' &&
        ls -la /app/node_modules/.prisma/client/libquery_engine-debian-openssl-3.0.x.so.node 2>/dev/null || echo 'Debian Query Engine binary not found' &&
        echo '' &&
        echo 'Checking Prisma Client package...' &&
        ls -la /app/node_modules/@prisma/client/ 2>/dev/null || echo 'Prisma client package not found' &&
        echo '' &&
        echo 'Checking OpenSSL installation...' &&
        openssl version 2>/dev/null || echo 'OpenSSL not found' &&
        echo '' &&
        echo 'Checking environment variables...' &&
        echo \"PRISMA_QUERY_ENGINE_LIBRARY: \$PRISMA_QUERY_ENGINE_LIBRARY\" &&
        echo \"PRISMA_QUERY_ENGINE_BINARY: \$PRISMA_QUERY_ENGINE_BINARY\"
    "

    # Cleanup temporary container
    docker rm "$temp_container" >/dev/null 2>&1 || true

    print_success "Prisma files inspection completed"
}

# Function to start test container
start_test_container() {
    print_status "Starting test container..."

    # Check if required environment variables are set
    if [ -z "${DATABASE_URL:-}" ]; then
        print_warning "DATABASE_URL not set. Using a dummy URL for testing."
        export DATABASE_URL="***********************************/dummy"
    fi

    if [ -z "${BETTER_AUTH_SECRET:-}" ]; then
        print_warning "BETTER_AUTH_SECRET not set. Using a dummy secret for testing."
        export BETTER_AUTH_SECRET="dummy-secret-for-testing-only"
    fi

    # Start the container
    docker run -d \
        --name "$CONTAINER_NAME" \
        -p "${TEST_PORT}:3000" \
        -e DATABASE_URL="$DATABASE_URL" \
        -e BETTER_AUTH_SECRET="$BETTER_AUTH_SECRET" \
        -e NODE_ENV=production \
        "${IMAGE_NAME}:${TAG}"

    print_success "Test container started: $CONTAINER_NAME"
    print_status "Container is running on port $TEST_PORT"
}

# Function to wait for container to be ready
wait_for_container() {
    print_status "Waiting for container to be ready..."

    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if docker logs "$CONTAINER_NAME" 2>&1 | grep -q "Ready in\|Listening on\|Server running"; then
            print_success "Container is ready!"
            return 0
        fi

        if docker logs "$CONTAINER_NAME" 2>&1 | grep -q "PrismaClientInitializationError\|Error.*Prisma"; then
            print_error "Prisma initialization error detected!"
            print_status "Container logs:"
            docker logs "$CONTAINER_NAME"
            return 1
        fi

        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done

    print_warning "Container did not become ready within expected time"
    print_status "Container logs:"
    docker logs "$CONTAINER_NAME"
    return 1
}

# Function to test container health
test_container_health() {
    print_status "Testing container health..."

    # Test if the container is running
    if ! docker ps | grep -q "$CONTAINER_NAME"; then
        print_error "Container is not running!"
        return 1
    fi

    # Test if the port is accessible
    if command -v curl >/dev/null 2>&1; then
        print_status "Testing HTTP endpoint..."
        if curl -f "http://localhost:${TEST_PORT}/api/health" >/dev/null 2>&1; then
            print_success "HTTP endpoint is accessible"
        else
            print_warning "HTTP endpoint test failed (this might be expected if no health endpoint exists)"
        fi
    else
        print_warning "curl not available, skipping HTTP test"
    fi

    # Check container logs for errors
    print_status "Checking container logs for errors..."
    local logs=$(docker logs "$CONTAINER_NAME" 2>&1)

    if echo "$logs" | grep -q "PrismaClientInitializationError"; then
        print_error "Prisma Client initialization error found in logs!"
        echo "$logs" | grep -A 5 -B 5 "PrismaClientInitializationError"
        return 1
    fi

    if echo "$logs" | grep -q "Query Engine.*not found"; then
        print_error "Query Engine not found error in logs!"
        echo "$logs" | grep -A 5 -B 5 "Query Engine"
        return 1
    fi

    print_success "No Prisma-related errors found in logs"
    return 0
}

# Function to show container logs
show_container_logs() {
    print_status "Container logs:"
    echo "----------------------------------------"
    docker logs "$CONTAINER_NAME" 2>&1 | tail -50
    echo "----------------------------------------"
}

# Function to cleanup test container
cleanup_test_container() {
    print_status "Cleaning up test container..."

    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    docker rm "$CONTAINER_NAME" 2>/dev/null || true

    print_status "Test container cleaned up"
}

# Main test function
run_prisma_test() {
    print_status "Starting Prisma Docker test..."

    # Cleanup any previous tests
    cleanup_previous_tests

    # Check if image exists
    check_image_exists

    # Test Prisma files in the image
    test_prisma_files_in_image

    # Start test container
    start_test_container

    # Wait for container to be ready
    if wait_for_container; then
        # Test container health
        if test_container_health; then
            print_success "✅ Prisma Docker test PASSED!"
            print_success "The container successfully initialized Prisma Client without errors."
        else
            print_error "❌ Prisma Docker test FAILED!"
            print_error "Container health check failed."
            show_container_logs
            cleanup_test_container
            exit 1
        fi
    else
        print_error "❌ Prisma Docker test FAILED!"
        print_error "Container failed to start properly."
        show_container_logs
        cleanup_test_container
        exit 1
    fi

    # Show final status
    print_status "Test completed. Container is still running for manual inspection."
    print_status "Container name: $CONTAINER_NAME"
    print_status "Port: $TEST_PORT"
    print_status ""
    print_status "To view logs: docker logs $CONTAINER_NAME"
    print_status "To stop container: docker stop $CONTAINER_NAME"
    print_status "To remove container: docker rm $CONTAINER_NAME"
    print_status "To cleanup: ./scripts/test-prisma-docker.sh --cleanup"
}

# Parse command line arguments
case "${1:-}" in
    --cleanup)
        cleanup_test_container
        exit 0
        ;;
    --help)
        echo "Usage: $0 [OPTIONS]"
        echo "Options:"
        echo "  --cleanup    Clean up test containers"
        echo "  --help       Show this help message"
        echo ""
        echo "Environment variables:"
        echo "  IMAGE_NAME   Docker image name (default: pluggou-web)"
        echo "  TAG          Docker image tag (default: latest)"
        echo "  TEST_PORT    Port for testing (default: 3001)"
        echo "  DATABASE_URL Database connection string"
        echo "  BETTER_AUTH_SECRET Auth secret"
        exit 0
        ;;
    "")
        run_prisma_test
        ;;
    *)
        print_error "Unknown option: $1"
        print_status "Use --help for usage information"
        exit 1
        ;;
esac
