#!/usr/bin/env node

/**
 * Test script for Flow2Pay webhook endpoint
 * Tests all webhook event types with realistic payloads based on Flow2Pay documentation
 */

const fetch = require('node-fetch');

const WEBHOOK_URL = process.env.NEXT_PUBLIC_SITE_URL
  ? `${process.env.NEXT_PUBLIC_SITE_URL}/baas`
  : 'http://localhost:3000/baas';

const EVENT_TOKEN = process.env.FLOW2PAY_EVENT_TOKEN || 'test_token_12345';

async function testWebhook() {
  console.log('🧪 Testing Flow2Pay webhook endpoint...');
  console.log(`📡 Webhook URL: ${WEBHOOK_URL}`);
  console.log(`🔑 Event Token: ${EVENT_TOKEN.substring(0, 8)}...`);

  // Test 1: PixIn event (received payment) - Processing
  console.log('\n1️⃣ Testing PixIn event (received payment - processing)...');
  const pixInProcessingPayload = {
    evento: 'PixIn',
    token: EVENT_TOKEN,
    txid: 'test_txid_' + Date.now(),
    endToEndId: 'E' + Math.floor(Math.random() * 100000000000000000000000000000000),
    codigoTransacao: 'codigo_' + Math.floor(Math.random() * 1000000),
    valor: 1050, // R$ 10.50 in centavos
    horario: new Date().toISOString(),
    status: 'Em processamento',
    chavePix: '<EMAIL>',
    pagador: {
      nome: 'João Silva',
      codigoBanco: '260',
      cpf_cnpj: '***123456**'
    },
    recebedor: {
      nome: 'Empresa LTDA',
      codigoBanco: '17846627',
      cpf_cnpj: '***654321**'
    }
  };

  await sendWebhook('PixIn Processing', pixInProcessingPayload);

  // Test 2: PixIn event (received payment) - Success
  console.log('\n2️⃣ Testing PixIn event (received payment - success)...');
  const pixInSuccessPayload = {
    ...pixInProcessingPayload,
    status: 'Sucesso',
    horario: new Date().toISOString()
  };

  await sendWebhook('PixIn Success', pixInSuccessPayload);

  // Test 3: PixOut event (sent payment) - Processing
  console.log('\n3️⃣ Testing PixOut event (sent payment - processing)...');
  const pixOutProcessingPayload = {
    evento: 'PixOut',
    token: EVENT_TOKEN,
    idEnvio: 'test_id_envio_' + Date.now(),
    endToEndId: 'E' + Math.floor(Math.random() * 100000000000000000000000000000000),
    codigoTransacao: 'codigo_' + Math.floor(Math.random() * 1000000),
    valor: -500, // R$ 5.00 in centavos (negative for outgoing)
    horario: new Date().toISOString(),
    status: 'Em processamento',
    chavePix: '<EMAIL>',
    recebedor: {
      nome: 'Maria Santos',
      codigoBanco: '341',
      cpf_cnpj: '***987654**'
    }
  };

  await sendWebhook('PixOut Processing', pixOutProcessingPayload);

  // Test 4: PixOut event (sent payment) - Success
  console.log('\n4️⃣ Testing PixOut event (sent payment - success)...');
  const pixOutSuccessPayload = {
    ...pixOutProcessingPayload,
    status: 'Sucesso',
    horario: new Date().toISOString()
  };

  await sendWebhook('PixOut Success', pixOutSuccessPayload);

  // Test 5: PixOut event (sent payment) - Failure
  console.log('\n5️⃣ Testing PixOut event (sent payment - failure)...');
  const pixOutFailurePayload = {
    ...pixOutProcessingPayload,
    status: 'Falha',
    endToEndId: null,
    horario: new Date().toISOString(),
    erro: {
      origem: 'Origem interna',
      motivo: 'Conta sem saldo'
    }
  };

  await sendWebhook('PixOut Failure', pixOutFailurePayload);

  // Test 6: PixInReversal event (refund of received payment) - Processing
  console.log('\n6️⃣ Testing PixInReversal event (refund - processing)...');
  const pixInReversalProcessingPayload = {
    evento: 'PixInReversal',
    token: EVENT_TOKEN,
    idEnvio: 'reversal_' + Date.now(),
    endToEndId: pixInSuccessPayload.endToEndId, // Reference to original transaction
    codigoTransacao: 'rev_codigo_' + Math.floor(Math.random() * 1000000),
    valor: -1050, // Negative amount for reversal
    horario: new Date().toISOString(),
    status: 'Em processamento',
    chavePix: null,
    recebedor: {
      nome: 'João Silva',
      codigoBanco: '260',
      cpf_cnpj: '***123456**'
    }
  };

  await sendWebhook('PixInReversal Processing', pixInReversalProcessingPayload);

  // Test 7: PixInReversal event (refund of received payment) - Success
  console.log('\n7️⃣ Testing PixInReversal event (refund - success)...');
  const pixInReversalSuccessPayload = {
    ...pixInReversalProcessingPayload,
    status: 'Sucesso',
    horario: new Date().toISOString()
  };

  await sendWebhook('PixInReversal Success', pixInReversalSuccessPayload);

  // Test 8: PixOutReversalExternal event (external refund of sent payment)
  console.log('\n8️⃣ Testing PixOutReversalExternal event (external refund)...');
  const pixOutReversalPayload = {
    evento: 'PixOutReversalExternal',
    token: EVENT_TOKEN,
    idEnvio: pixOutSuccessPayload.idEnvio, // Reference to original transaction
    endToEndId: pixOutSuccessPayload.endToEndId,
    codigoTransacao: 'ext_rev_' + Math.floor(Math.random() * 1000000),
    valor: 500, // Positive amount for reversal (money coming back)
    horario: new Date().toISOString(),
    status: 'Sucesso',
    chavePix: null,
    recebedor: {
      nome: null,
      codigoBanco: '341',
      cpf_cnpj: '***987654**'
    },
    erro: {
      origem: 'Origem externa',
      motivo: 'Pedido de reembolso'
    }
  };

  await sendWebhook('PixOutReversalExternal', pixOutReversalPayload);

  // Test 9: Invalid event type
  console.log('\n9️⃣ Testing invalid event type...');
  const invalidEventPayload = {
    evento: 'InvalidEvent',
    token: EVENT_TOKEN,
    txid: 'invalid_test',
    status: 'Sucesso'
  };

  await sendWebhook('Invalid Event', invalidEventPayload, true);

  // Test 10: Missing evento field
  console.log('\n🔟 Testing missing evento field...');
  const missingEventoPayload = {
    token: EVENT_TOKEN,
    txid: 'missing_evento_test',
    status: 'Sucesso'
  };

  await sendWebhook('Missing Evento', missingEventoPayload, true);

  // Test 11: Invalid token
  console.log('\n1️⃣1️⃣ Testing invalid token...');
  const invalidTokenPayload = {
    evento: 'PixIn',
    token: 'invalid_token_123',
    txid: 'invalid_token_test',
    status: 'Sucesso'
  };

  await sendWebhook('Invalid Token', invalidTokenPayload, true);

  console.log('\n✅ All webhook tests completed!');
}

async function sendWebhook(testName, payload, expectError = false) {
  try {
    console.log(`   📤 Sending ${testName} webhook...`);
    console.log(`   📋 Payload:`, JSON.stringify(payload, null, 2));

    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Flow2Pay-Webhook-Test/1.0'
      },
      body: JSON.stringify(payload)
    });

    const responseText = await response.text();
    console.log(`   📊 Status: ${response.status}`);
    console.log(`   📄 Response: ${responseText}`);

    if (expectError) {
      if (response.status >= 400) {
        console.log(`   ✅ ${testName} test passed (expected error)`);
      } else {
        console.log(`   ⚠️ ${testName} test unexpected success (expected error)`);
      }
    } else {
      if (response.ok) {
        console.log(`   ✅ ${testName} test passed`);
      } else {
        console.log(`   ❌ ${testName} test failed`);
      }
    }
  } catch (error) {
    console.log(`   ❌ ${testName} test error: ${error.message}`);
  }
}

// Run the tests
if (require.main === module) {
  testWebhook().catch(console.error);
}

module.exports = { testWebhook };
