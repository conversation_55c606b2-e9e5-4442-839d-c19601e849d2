# Gateway Configuration Guide

## Default Payment Gateway Configuration

O sistema agora suporta configuração rápida do gateway padrão através de variáveis de ambiente, otimizando a velocidade das transações.

### Configuração por Variável de Ambiente

Para máxima performance, você pode definir o gateway padrão e suas credenciais usando variáveis de ambiente:

```bash
# Gateway padrão
DEFAULT_PAYMENT_GATEWAY=PLUGGOU_PIX

# Credenciais do PLUGGOU_PIX (exemplo)
PLUGGOU_PIX_API_KEY=your_api_key_here
PLUGGOU_PIX_API_URL=https://apipix.cloud.pluggou.io
PLUGGOU_PIX_ENVIRONMENT=production
PLUGGOU_PIX_WEBHOOK_SECRET=your_webhook_secret
```

### Gateways Suportados

- `PLUGGOU_PIX` - Gateway principal (padrão)
- `FLOW2PAY` - Integração direta Flow2Pay
- `TRANSFEERA` - Para transferências PIX
- `PIXIUM` - Gateway Pixium
- `REFLOWPAY` - Gateway ReflowPay
- `PRIMEPAG` - Gateway PrimePag
- `MEDIUSPAG` - Gateway MediusPag
- `MOCKSIM` - Gateway de simulação para testes

### Prioridade de Seleção

1. **Variável de Ambiente**: Se `DEFAULT_PAYMENT_GATEWAY` estiver definida, será usada como gateway principal
2. **Fallback**: Se não definida, o sistema usa `PLUGGOU_PIX` como padrão
3. **Configuração do Banco**: Como fallback final, o sistema consulta a tabela `payment_gateways`

### Benefícios da Configuração por Ambiente

- ⚡ **Velocidade**: Elimina consultas ao banco de dados para seleção do gateway padrão
- 🚀 **Performance**: Reduz latência nas transações PIX eliminando consultas de credenciais
- 🔧 **Flexibilidade**: Permite mudança rápida do gateway sem alteração no banco
- 🎯 **Simplicidade**: Configuração centralizada via variáveis de ambiente
- 🔒 **Segurança**: Credenciais ficam no ambiente, não no banco de dados
- 📈 **Escalabilidade**: Zero consultas ao banco para gateway e credenciais padrão

### Exemplo de Configuração

```bash
# .env.local

# Gateway padrão
DEFAULT_PAYMENT_GATEWAY=PLUGGOU_PIX

# Credenciais PLUGGOU_PIX
PLUGGOU_PIX_API_KEY=pk_live_abc123...
PLUGGOU_PIX_API_URL=https://apipix.cloud.pluggou.io
PLUGGOU_PIX_ENVIRONMENT=production
PLUGGOU_PIX_WEBHOOK_SECRET=whsec_abc123...

# Credenciais FLOW2PAY (se necessário)
FLOW2PAY_CLIENT_ID=your_flow2pay_client_id
FLOW2PAY_CLIENT_SECRET=your_flow2pay_client_secret
FLOW2PAY_EVENT_TOKEN=your_flow2pay_event_token
FLOW2PAY_API_URL=https://pixv2.flow2pay.com.br
FLOW2PAY_ENVIRONMENT=production

# Credenciais TRANSFEERA (se necessário)
TRANSFEERA_CLIENT_ID=your_client_id
TRANSFEERA_CLIENT_SECRET=your_client_secret
TRANSFEERA_ENVIRONMENT=production
```

### Variáveis de Ambiente por Gateway

#### PLUGGOU_PIX
```bash
PLUGGOU_PIX_API_KEY=your_api_key
PLUGGOU_PIX_API_URL=https://apipix.cloud.pluggou.io
PLUGGOU_PIX_ENVIRONMENT=sandbox|production
PLUGGOU_PIX_WEBHOOK_SECRET=your_webhook_secret
```

#### FLOW2PAY
```bash
FLOW2PAY_CLIENT_ID=your_client_id
FLOW2PAY_CLIENT_SECRET=your_client_secret
FLOW2PAY_EVENT_TOKEN=your_event_token
FLOW2PAY_API_URL=https://pixv2.flow2pay.com.br
FLOW2PAY_ENVIRONMENT=sandbox|production
FLOW2PAY_TIMEOUT=15000
FLOW2PAY_RETRIES=2
```

#### TRANSFEERA
```bash
TRANSFEERA_CLIENT_ID=your_client_id
TRANSFEERA_CLIENT_SECRET=your_client_secret
TRANSFEERA_ENVIRONMENT=sandbox|production
TRANSFEERA_WEBHOOK_SECRET=your_webhook_secret
```

#### PIXIUM
```bash
PIXIUM_API_KEY=your_api_key
PIXIUM_API_SECRET=your_api_secret
PIXIUM_ENVIRONMENT=sandbox|production
PIXIUM_WEBHOOK_SECRET=your_webhook_secret
```

#### REFLOWPAY
```bash
REFLOWPAY_API_KEY=your_api_key
REFLOWPAY_API_SECRET=your_api_secret
REFLOWPAY_ENVIRONMENT=sandbox|production
REFLOWPAY_WEBHOOK_SECRET=your_webhook_secret
```

#### PRIMEPAG
```bash
PRIMEPAG_API_KEY=your_api_key
PRIMEPAG_API_SECRET=your_api_secret
PRIMEPAG_ENVIRONMENT=sandbox|production
PRIMEPAG_WEBHOOK_SECRET=your_webhook_secret
```

#### MEDIUSPAG
```bash
MEDIUSPAG_API_KEY=your_api_key
MEDIUSPAG_API_SECRET=your_api_secret
MEDIUSPAG_ENVIRONMENT=sandbox|production
MEDIUSPAG_WEBHOOK_SECRET=your_webhook_secret
```

### Logs de Monitoramento

O sistema registra logs detalhados para monitoramento:

```
🚀 Using PLUGGOU_PIX as PRIMARY gateway for maximum transaction speed
🚀 Using environment credentials for default gateway PLUGGOU_PIX
✅ Environment credentials found for PLUGGOU_PIX
```

### Configuração Avançada via Painel

Para configurações mais complexas, use o painel administrativo em `/admin/gateways` onde você pode:

- Configurar múltiplos gateways
- Definir prioridades
- Configurar credenciais específicas por organização
- Ativar/desativar gateways

### Migração

Se você estava usando outro gateway como padrão, simplesmente defina a variável de ambiente:

```bash
# Para usar Flow2Pay como padrão
DEFAULT_PAYMENT_GATEWAY=FLOW2PAY

# Para usar Transfeera como padrão
DEFAULT_PAYMENT_GATEWAY=TRANSFEERA
```

### Troubleshooting

1. **Gateway não encontrado**: Verifique se o nome está correto e em maiúsculas
2. **Credenciais por ENV**:
   - Verifique se as variáveis de ambiente estão definidas corretamente
   - Use o padrão `GATEWAY_TYPE_CREDENTIAL_NAME` (ex: `PLUGGOU_PIX_API_KEY`)
   - Reinicie a aplicação após alterar variáveis de ambiente
3. **Fallback para banco**: Se as credenciais ENV não estiverem disponíveis, o sistema usa o banco
4. **Logs**: Monitore os logs para verificar qual gateway e fonte de credenciais está sendo usado
5. **Cache**: Credenciais são cacheadas por 5 minutos para performance

### Verificação de Configuração

Para verificar se as credenciais estão sendo carregadas corretamente, observe os logs:

```bash
# Credenciais carregadas do ambiente (ideal)
🚀 Using environment credentials for default gateway PLUGGOU_PIX
✅ Environment credentials found for PLUGGOU_PIX

# Fallback para banco de dados
⚠️ No environment credentials found for default gateway PLUGGOU_PIX, falling back to database
```

### Suporte

Para dúvidas ou problemas, consulte os logs do sistema ou entre em contato com o suporte técnico.
