name: Docker Build and Deploy

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'apps/web/**'
      - 'packages/**'
      - 'config/**'
      - 'tooling/**'
      - 'package.json'
      - 'pnpm-lock.yaml'
      - 'turbo.json'
      - 'apps/web/Dockerfile'
      - '.dockerignore'
  pull_request:
    branches: [ main ]
    paths:
      - 'apps/web/**'
      - 'packages/**'
      - 'config/**'
      - 'tooling/**'
      - 'package.json'
      - 'pnpm-lock.yaml'
      - 'turbo.json'
      - 'apps/web/Dockerfile'
      - '.dockerignore'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/web

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      with:
        driver-opts: |
          network=host

    - name: Log in to Container Registry
      if: github.event_name != 'pull_request'
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./apps/web/Dockerfile
        platforms: linux/amd64,linux/arm64
        push: ${{ github.event_name != 'pull_request' }}
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        build-args: |
          BUILDKIT_INLINE_CACHE=1

    - name: Test Docker image
      if: github.event_name == 'pull_request'
      run: |
        docker run --rm -d --name test-container -p 3000:3000 ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.meta.outputs.version }}
        sleep 30
        curl -f http://localhost:3000/api/health || exit 1
        docker stop test-container

  security-scan:
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name != 'pull_request'
    
    steps:
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'
