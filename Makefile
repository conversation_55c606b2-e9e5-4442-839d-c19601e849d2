# Docker Build Makefile for Pluggou
# Provides convenient commands for Docker operations

# Variables
IMAGE_NAME ?= pluggou-web
TAG ?= latest
DOCKERFILE ?= apps/web/Dockerfile
PLATFORM ?= linux/amd64

# Colors for output
BLUE = \033[0;34m
GREEN = \033[0;32m
YELLOW = \033[1;33m
RED = \033[0;31m
NC = \033[0m # No Color

.PHONY: help build build-fast build-optimized build-no-cache run run-dev stop clean logs health test-build test-prisma debug-prisma push pull

# Default target
help: ## Show this help message
	@echo "$(BLUE)Pluggou Docker Build Commands$(NC)"
	@echo ""
	@echo "$(YELLOW)Usage:$(NC)"
	@echo "  make <target>"
	@echo ""
	@echo "$(YELLOW)Targets:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GRE<PERSON>)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(YELLOW)Variables:$(NC)"
	@echo "  IMAGE_NAME=$(IMAGE_NAME)"
	@echo "  TAG=$(TAG)"
	@echo "  DOCKERFILE=$(DOCKERFILE)"
	@echo "  PLATFORM=$(PLATFORM)"

build: ## Build Docker image with optimizations
	@echo "$(BLUE)Building Docker image: $(IMAGE_NAME):$(TAG)$(NC)"
	@./scripts/docker-build.sh --name $(IMAGE_NAME) --tag $(TAG) --context .

build-fast: ## Build with cache and parallel processing
	@echo "$(BLUE)Fast build with cache: $(IMAGE_NAME):$(TAG)$(NC)"
	@./scripts/docker-build.sh --name $(IMAGE_NAME) --tag $(TAG) --context . --no-cleanup

build-optimized: ## Build with memory optimization (recommended for large builds)
	@echo "$(BLUE)Optimized build with memory management: $(IMAGE_NAME):$(TAG)$(NC)"
	@./scripts/docker-build-optimized.sh --name $(IMAGE_NAME) --tag $(TAG)

build-no-cache: ## Build without cache (clean build)
	@echo "$(BLUE)Clean build without cache: $(IMAGE_NAME):$(TAG)$(NC)"
	@./scripts/docker-build.sh --name $(IMAGE_NAME) --tag $(TAG) --context . --no-cache

build-dev: ## Build development image
	@echo "$(BLUE)Building development image$(NC)"
	@docker-compose build web-dev

run: ## Run production container
	@echo "$(BLUE)Starting production container$(NC)"
	@docker-compose up -d web
	@echo "$(GREEN)Container started. Access at http://localhost:3000$(NC)"

run-dev: ## Run development container with hot reload
	@echo "$(BLUE)Starting development container$(NC)"
	@docker-compose --profile dev up web-dev

run-detached: ## Run container in background
	@echo "$(BLUE)Starting container in background$(NC)"
	@docker run -d --name $(IMAGE_NAME) -p 3000:3000 $(IMAGE_NAME):$(TAG)
	@echo "$(GREEN)Container started in background$(NC)"

stop: ## Stop running containers
	@echo "$(BLUE)Stopping containers$(NC)"
	@docker-compose down
	@docker stop $(IMAGE_NAME) 2>/dev/null || true
	@docker rm $(IMAGE_NAME) 2>/dev/null || true

logs: ## Show container logs
	@echo "$(BLUE)Showing container logs$(NC)"
	@docker-compose logs -f web

logs-dev: ## Show development container logs
	@echo "$(BLUE)Showing development container logs$(NC)"
	@docker-compose logs -f web-dev

health: ## Check container health
	@echo "$(BLUE)Checking container health$(NC)"
	@docker-compose ps
	@echo ""
	@curl -f http://localhost:3000/api/health || echo "$(RED)Health check failed$(NC)"

shell: ## Open shell in running container
	@echo "$(BLUE)Opening shell in container$(NC)"
	@docker-compose exec web sh

shell-dev: ## Open shell in development container
	@echo "$(BLUE)Opening shell in development container$(NC)"
	@docker-compose exec web-dev sh

test-build: ## Test build without running
	@echo "$(BLUE)Testing build process$(NC)"
	@./scripts/docker-build.sh --name $(IMAGE_NAME)-test --tag test --context . --no-verify

test-docker: ## Test Docker build with error detection
	@echo "$(BLUE)Testing Docker build process$(NC)"
	@./scripts/test-docker-build.sh

test-prisma: ## Test Prisma Client in Docker container
	@echo "$(BLUE)Testing Prisma Client in Docker$(NC)"
	@./scripts/test-prisma-docker.sh

debug-prisma: ## Debug Prisma generation issues
	@echo "$(BLUE)Debugging Prisma generation$(NC)"
	@./scripts/debug-prisma-generation.sh all

clean: ## Clean up Docker resources
	@echo "$(BLUE)Cleaning up Docker resources$(NC)"
	@docker-compose down --rmi all --volumes --remove-orphans 2>/dev/null || true
	@docker image prune -f
	@docker container prune -f
	@docker volume prune -f
	@docker builder prune -f
	@echo "$(GREEN)Cleanup completed$(NC)"

fix-lockfile: ## Fix pnpm lockfile issues
	@echo "$(BLUE)Fixing pnpm lockfile issues$(NC)"
	@./scripts/fix-lockfile.sh

verify-lockfile: ## Verify pnpm lockfile consistency
	@echo "$(BLUE)Verifying pnpm lockfile$(NC)"
	@./scripts/fix-lockfile.sh --verify-only

clean-all: ## Clean all Docker resources (including cache)
	@echo "$(YELLOW)Warning: This will remove ALL Docker resources$(NC)"
	@read -p "Are you sure? [y/N] " -n 1 -r; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		echo ""; \
		echo "$(BLUE)Cleaning all Docker resources$(NC)"; \
		docker system prune -a -f --volumes; \
		docker builder prune -a -f; \
		echo "$(GREEN)All Docker resources cleaned$(NC)"; \
	else \
		echo ""; \
		echo "$(YELLOW)Cleanup cancelled$(NC)"; \
	fi

stats: ## Show Docker resource usage
	@echo "$(BLUE)Docker resource usage:$(NC)"
	@docker system df
	@echo ""
	@echo "$(BLUE)Running containers:$(NC)"
	@docker stats --no-stream

inspect: ## Inspect built image
	@echo "$(BLUE)Inspecting image: $(IMAGE_NAME):$(TAG)$(NC)"
	@docker image inspect $(IMAGE_NAME):$(TAG) | jq '.[0] | {Id, Size, Created, Config: {Env, ExposedPorts, Cmd}}'

size: ## Show image size
	@echo "$(BLUE)Image sizes:$(NC)"
	@docker images $(IMAGE_NAME) --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

push: ## Push image to registry
	@echo "$(BLUE)Pushing image: $(IMAGE_NAME):$(TAG)$(NC)"
	@docker push $(IMAGE_NAME):$(TAG)

pull: ## Pull image from registry
	@echo "$(BLUE)Pulling image: $(IMAGE_NAME):$(TAG)$(NC)"
	@docker pull $(IMAGE_NAME):$(TAG)

# Development helpers
dev-setup: ## Setup development environment
	@echo "$(BLUE)Setting up development environment$(NC)"
	@pnpm install
	@echo "$(GREEN)Development environment ready$(NC)"

dev-build: ## Build for development
	@echo "$(BLUE)Building for development$(NC)"
	@pnpm build

dev-start: ## Start development server
	@echo "$(BLUE)Starting development server$(NC)"
	@pnpm dev

# CI/CD helpers
ci-build: ## Build for CI/CD (with specific optimizations)
	@echo "$(BLUE)Building for CI/CD$(NC)"
	@./scripts/docker-build.sh --name $(IMAGE_NAME) --tag $(TAG) --context . --no-verify --memory 8g

ci-test: ## Run tests in container
	@echo "$(BLUE)Running tests in container$(NC)"
	@docker run --rm $(IMAGE_NAME):$(TAG) pnpm test

# Monitoring
monitor: ## Monitor container performance
	@echo "$(BLUE)Monitoring container performance$(NC)"
	@watch -n 2 'docker stats --no-stream'

# Quick commands
up: run ## Alias for run
down: stop ## Alias for stop
rebuild: clean build ## Clean and rebuild
restart: stop run ## Stop and restart
