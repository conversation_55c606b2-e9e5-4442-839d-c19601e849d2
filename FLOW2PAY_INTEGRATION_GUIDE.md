# Flow2Pay Integration Guide

This guide provides step-by-step instructions for configuring Flow2Pay as your default payment gateway and setting up webhook integration.

## Overview

Flow2Pay integration eliminates the intermediate Pluggou PIX layer, providing:
- **Direct API Integration** - Connects directly to Flow2Pay's REST API
- **Improved Performance** - Reduces response times by 200-500ms
- **Complete PIX Support** - Handles payments, transfers, and reversals
- **Webhook Integration** - Real-time event processing via `/api/webhooks/flow2pay/baas`

## Prerequisites

Before starting, ensure you have:
1. Flow2Pay account with API credentials
2. Access to your application's admin panel
3. Database access for configuration
4. Environment variables configured

## Required Credentials

You'll need the following credentials from Flow2Pay:
- **Client ID** - Your Flow2Pay client identifier
- **Client Secret** - Your Flow2Pay client secret
- **Event Token** - Token for webhook authentication
- **API URL** - Flow2Pay API endpoint (default: `https://pixv2.flow2pay.com.br`)

## Step 1: Environment Configuration

Add the following environment variables to your `.env` file:

```env
# Flow2Pay Configuration
FLOW2PAY_CLIENT_ID=your_flow2pay_client_id
FLOW2PAY_CLIENT_SECRET=your_flow2pay_client_secret
FLOW2PAY_EVENT_TOKEN=your_flow2pay_event_token
FLOW2PAY_API_URL=https://pixv2.flow2pay.com.br

# Your organization ID (for automatic setup)
ORGANIZATION_ID=your_organization_id
```

## Step 2: Database Setup

Run the setup script to configure Flow2Pay as your default gateway:

```bash
# Make the script executable
chmod +x scripts/setup-flow2pay-gateway.js

# Run the setup script
node scripts/setup-flow2pay-gateway.js
```

This script will:
- Create or update the Flow2Pay gateway in the database
- Set it as the default gateway
- Configure it for both receiving and sending PIX payments
- Associate it with your organization (if ORGANIZATION_ID is provided)

## Step 3: Admin Panel Configuration

1. **Access Admin Panel**
   - Navigate to `/admin/gateways` in your application
   - You should see "Flow2Pay" in the available gateways list

2. **Configure Gateway**
   - Click on "Flow2Pay" to configure
   - Enter your credentials:
     - Client ID
     - Client Secret
     - Event Token
     - API URL (pre-filled)
   - Set as active and default
   - Save configuration

## Step 4: Organization Gateway Setup

1. **Access Organization Settings**
   - Navigate to `/[organizationSlug]/integrations/gateways`
   - Find "Flow2Pay" in the available gateways

2. **Configure for Organization**
   - Click "Configure" on Flow2Pay
   - Enter the same credentials as in admin panel
   - Set as active and default
   - Configure priority (lower number = higher priority)
   - Save configuration

## Step 5: Webhook Configuration

### In Your Application

The webhook endpoint is automatically created at:
```
https://your-domain.com/api/webhooks/flow2pay/baas
```

### In Flow2Pay Dashboard

1. **Access Flow2Pay Dashboard**
   - Log into your Flow2Pay account
   - Navigate to webhook configuration

2. **Configure Webhook URL**
   - Set webhook URL to: `https://your-domain.com/api/webhooks/flow2pay/baas`
   - Ensure all event types are enabled:
     - PixIn (received payments)
     - PixOut (sent payments)
     - PixInReversal (refunds of received payments)
     - PixOutReversalExternal (refunds of sent payments)

3. **Authentication**
   - The webhook uses the Event Token for authentication
   - Ensure the token in Flow2Pay matches your `FLOW2PAY_EVENT_TOKEN`

## Step 6: Testing the Integration

### Test PIX Payment Creation

```bash
# Test creating a PIX payment
curl -X POST https://your-domain.com/api/pix/charge \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_api_key" \
  -d '{
    "amount": 10.50,
    "description": "Test payment",
    "organizationId": "your_org_id"
  }'
```

### Test PIX Transfer

```bash
# Test sending a PIX transfer
curl -X POST https://your-domain.com/api/pix/transfer \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_api_key" \
  -d '{
    "amount": 5.00,
    "pixKey": "<EMAIL>",
    "pixKeyType": "EMAIL",
    "description": "Test transfer",
    "organizationId": "your_org_id"
  }'
```

### Monitor Webhook Events

Check your application logs for webhook events:
```bash
# Monitor webhook processing
tail -f logs/application.log | grep "Flow2Pay webhook"
```

## Webhook Event Types

The integration handles the following Flow2Pay events:

### PixIn Events
- **Event**: Payment received
- **Trigger**: When someone pays a PIX QR code
- **Action**: Updates transaction status to completed
- **Webhook**: Triggers `pix.payment.completed` event

### PixOut Events
- **Event**: Payment sent
- **Trigger**: When a PIX transfer is processed
- **Action**: Updates transaction status based on Flow2Pay status
- **Webhook**: Triggers `pix.transfer.completed` or `pix.transfer.failed`

### PixInReversal Events
- **Event**: Refund of received payment
- **Trigger**: When a received payment is reversed
- **Action**: Creates reversal transaction
- **Webhook**: Triggers `pix.reversal.completed` event

### PixOutReversalExternal Events
- **Event**: Refund of sent payment
- **Trigger**: When a sent payment is reversed
- **Action**: Creates reversal transaction
- **Webhook**: Triggers `pix.reversal.completed` event

## Troubleshooting

### Common Issues

1. **Webhook Authentication Fails**
   - Verify `FLOW2PAY_EVENT_TOKEN` matches Flow2Pay configuration
   - Check webhook logs for authentication errors

2. **Transaction Not Found**
   - Ensure transaction IDs match between creation and webhook
   - Check external ID mapping in database

3. **Gateway Not Available**
   - Verify gateway is active and default in database
   - Check organization gateway assignment

4. **API Calls Fail**
   - Verify Flow2Pay credentials are correct
   - Check API URL configuration
   - Monitor Flow2Pay API status

### Debug Commands

```bash
# Check gateway configuration
node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
prisma.paymentGateway.findMany({ where: { type: 'FLOW2PAY' } })
  .then(console.log)
  .finally(() => prisma.\$disconnect());
"

# Check organization gateway assignment
node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
prisma.organizationGateway.findMany({
  where: { organizationId: 'your_org_id' },
  include: { gateway: true }
}).then(console.log).finally(() => prisma.\$disconnect());
"
```

## Performance Monitoring

The Flow2Pay integration includes performance monitoring:
- Response times are logged for all API calls
- Webhook processing times are tracked
- Error rates and retry attempts are monitored

Monitor these metrics in your application logs to ensure optimal performance.

## Security Considerations

1. **Credential Security**
   - Store credentials in environment variables
   - Never commit credentials to version control
   - Rotate credentials regularly

2. **Webhook Security**
   - Webhook endpoint validates event tokens
   - All webhook payloads are logged for audit
   - Failed authentication attempts are monitored

3. **API Security**
   - All API calls use HTTPS
   - Access tokens are refreshed automatically
   - Rate limiting is handled gracefully

## Support

For issues with the Flow2Pay integration:
1. Check application logs for error details
2. Verify configuration using debug commands
3. Contact Flow2Pay support for API-related issues
4. Review webhook event logs for processing issues

## Migration from Pluggou PIX

If migrating from Pluggou PIX:
1. Complete Flow2Pay setup as described above
2. Test thoroughly in staging environment
3. Update gateway priority to prefer Flow2Pay
4. Monitor performance improvements
5. Gradually phase out Pluggou PIX usage
